
import React from 'react';

const Subscribe: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 bg-subscribe-pattern bg-cover bg-center relative overflow-hidden">
      {/* Advanced eco-psychology overlay with sophisticated opacity */}
      <div className="absolute inset-0 bg-gradient-overlay-water opacity-overlay-dominant"></div>

      {/* Sophisticated eco-floating elements with psychology-based opacity */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-32 h-32 bg-meadow-spring rounded-full blur-3xl eco-organic-float opacity-atmosphere-far"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-water-surface rounded-full blur-3xl eco-organic-float opacity-atmosphere-mid" style={{ animationDelay: '4s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-forest-light rounded-full blur-2xl eco-organic-float opacity-atmosphere-near" style={{ animationDelay: '8s' }}></div>
        <div className="absolute bottom-1/3 right-1/4 w-28 h-28 bg-sunrise-gold rounded-full blur-2xl eco-opacity-wave opacity-eco-breath" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
        <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4 font-poppins eco-text-shadow opacity-content-primary eco-fade-in-energy">
          🌿 Subscribe to Our Eco-Newsletter
        </h2>
        <p className="text-lg text-white mb-8 max-w-xl mx-auto font-montserrat leading-relaxed opacity-content-secondary eco-fade-in-trust">
          Get the latest sustainable travel updates, exclusive eco-friendly deals, and inspiring green stories delivered straight to your inbox.
        </p>
        <form className="max-w-lg mx-auto flex flex-col sm:flex-row gap-4">
          <input
            type="email"
            placeholder="Enter your email for eco-updates"
            className="flex-grow px-6 py-4 rounded-xl text-brand-teal focus:outline-none focus:ring-2 focus:ring-brand-green shadow-eco-lg font-montserrat bg-brand-cream backdrop-blur-sm border-2 border-brand-mint/30 focus:border-brand-green transition-all duration-300 opacity-content-primary"
            required
          />
          <button
            type="submit"
            className="eco-btn-secondary px-8 py-4 rounded-xl font-semibold transition-all duration-300 font-poppins shadow-eco-lg transform hover:scale-105 eco-float opacity-energy"
          >
            🌱 Subscribe
          </button>
        </form>
      </div>
    </section>
  );
};

export default Subscribe;
