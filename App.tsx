
import React, { useState, useEffect, useCallback } from 'react';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import HomePage from './pages/HomePage';
import AboutPage from './pages/AboutPage';
import PackagesPage from './pages/PackagesPage';
import PackageDetailsPage from './pages/PackageDetailsPage';
import ServicesPage from './pages/ServicesPage';
import BlogPage from './pages/BlogPage';
import ContactPage from './pages/ContactPage';
import FloatingSocialIcons from './components/FloatingSocialIcons'; // Added import
import { ScrollIcon } from './components/icons/ScrollIcon';
import { OUR_PACKAGES_DATA } from './constants';
import type { PackageDeal } from './types';

const App: React.FC = () => {
  const [activePage, setActivePage] = useState('home');
  const [selectedPackageId, setSelectedPackageId] = useState<string | null>(null);

  const handleNavigate = useCallback((keyOrId: string, isPage?: boolean) => {
    setSelectedPackageId(null);
    if (isPage) {
      setActivePage(keyOrId);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      if (activePage !== 'home' && keyOrId.startsWith('#')) {
        setActivePage('home');
        sessionStorage.setItem('scrollToId', keyOrId.substring(1));
      } else {
        const element = document.getElementById(keyOrId.substring(1));
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    }
  }, [activePage]);

  const handleViewPackageDetails = useCallback((packageId: string) => {
    setSelectedPackageId(packageId);
    setActivePage('packageDetail');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const handleBackToPackages = useCallback(() => {
    setSelectedPackageId(null);
    setActivePage('packages');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const handleBookNow = useCallback((packageId: string) => {
    alert(`Booking package ID: ${packageId}. Integration with a booking system is required.`);
  }, []);

  const handleReadPost = useCallback((postId: string) => {
    alert(`Reading blog post: ${postId}. Blog post detail page coming soon!`);
  }, []);

  useEffect(() => {
    const targetId = sessionStorage.getItem('scrollToId');
    if (activePage === 'home' && targetId) {
      const element = document.getElementById(targetId);
      if (element) {
        setTimeout(() => {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            sessionStorage.removeItem('scrollToId');
        }, 100);
      } else {
        sessionStorage.removeItem('scrollToId');
      }
    }
  }, [activePage]);

  const getSelectedPackageData = useCallback((): PackageDeal | undefined => {
    if (!selectedPackageId) return undefined;
    return OUR_PACKAGES_DATA.find(pkg => pkg.id === selectedPackageId);
  }, [selectedPackageId]);

  return (
    <div className="font-poppins bg-white text-brand-blue-dark">
      <Navbar onNavigate={handleNavigate} currentPage={activePage} />
      <FloatingSocialIcons /> {/* Added FloatingSocialIcons */}

      <main>
        {activePage === 'home' && <HomePage onViewPackageDetails={handleViewPackageDetails} />}
        {activePage === 'about' && <div className="pt-20 lg:pt-24"><AboutPage /></div>}
        {activePage === 'packages' && <div className="pt-20 lg:pt-24"><PackagesPage onViewDetails={handleViewPackageDetails} /></div>}
        {activePage === 'services' && <div className="pt-20 lg:pt-24"><ServicesPage /></div>}
        {activePage === 'blog' && <div className="pt-20 lg:pt-24"><BlogPage onReadPost={handleReadPost} /></div>}
        {activePage === 'contact' && <div className="pt-20 lg:pt-24"><ContactPage /></div>}
        {activePage === 'packageDetail' && selectedPackageId && (
          <div className="pt-20 lg:pt-24">
            <PackageDetailsPage
              packageData={getSelectedPackageData()}
              onBackToPackages={handleBackToPackages}
              onBookNow={handleBookNow}
            />
          </div>
        )}
      </main>

      <Footer />

      <div className="fixed bottom-10 right-[-30px] transform -rotate-90 origin-bottom-right eco-gradient-primary text-white px-6 py-3 rounded-t-md eco-shadow-lg z-50 hidden md:flex items-center space-x-2 cursor-pointer hover:eco-gradient-secondary transition-all duration-300">
        <ScrollIcon className="w-5 h-5 rotate-90"/>
        <span>Explore Nature!</span>
      </div>
    </div>
  );
};

export default App;
