import React from 'react';
import { FacebookIcon, InstagramIcon } from './icons/SocialIcons';
import { WhatsAppIcon } from './icons/WhatsAppIcon';

interface SocialLinkItem {
  name: string;
  Icon: React.ElementType;
  href: string;
  bgColor: string;
  hoverBgColor: string;
  textColor: string;
  ariaLabel: string;
}

const socialLinks: SocialLinkItem[] = [
  {
    name: 'WhatsApp',
    Icon: WhatsAppIcon,
    href: 'https://wa.me/1234567890', // Replace with your WhatsApp number
    bgColor: 'bg-green-500',
    hoverBgColor: 'hover:bg-green-600',
    textColor: 'text-white',
    ariaLabel: 'Chat on WhatsApp',
  },
  {
    name: 'Facebook',
    Icon: FacebookIcon,
    href: 'https://facebook.com/rebelrover', // Replace with your Facebook page
    bgColor: 'bg-blue-600',
    hoverBgColor: 'hover:bg-blue-700',
    textColor: 'text-white',
    ariaLabel: 'Follow us on Facebook',
  },
  {
    name: 'Instagram',
    Icon: InstagramIcon,
    href: 'https://instagram.com/rebelrover', // Replace with your Instagram page
    bgColor: 'bg-pink-500',
    hoverBgColor: 'hover:bg-pink-600',
    textColor: 'text-white',
    ariaLabel: 'Follow us on Instagram',
  },
];

const FloatingSocialIcons: React.FC = () => {
  return (
    <div className="fixed top-1/2 right-0 transform -translate-y-1/2 z-40 flex flex-col items-end">
      {socialLinks.map((social, index) => (
        <a
          key={social.name}
          href={social.href}
          target="_blank"
          rel="noopener noreferrer"
          className={`
            flex items-center justify-center
            h-12 my-1 pl-3 pr-2
            ${social.bgColor} ${social.hoverBgColor} ${social.textColor}
            rounded-l-lg shadow-lg
            transition-all duration-300 ease-in-out
            group focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
            w-12 hover:w-40
          `}
          aria-label={social.ariaLabel}
        >
          <social.Icon className="w-6 h-6 flex-shrink-0" />
          <span
            className="
              ml-2 text-sm font-medium
              opacity-0 group-hover:opacity-100
              transition-opacity duration-200 delay-100
              whitespace-nowrap overflow-hidden
            "
          >
            {social.name === 'Instagram' ? 'Follow Us' : social.name === 'Facebook' ? 'Like Us' : 'Chat Now'}
          </span>
        </a>
      ))}
    </div>
  );
};

export default FloatingSocialIcons;
