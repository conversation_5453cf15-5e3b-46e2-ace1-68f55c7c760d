import React, { useState } from 'react';
import type { PackageDeal } from '../types';
import { StarIcon } from '../components/icons/StarIcon';
import { LocationMarkerIconOutline } from '../components/icons/HeroIcons'; // Using existing icon

interface PackageDetailsPageProps {
  packageData: PackageDeal | undefined;
  onBackToPackages: () => void;
  onBookNow: (packageId: string) => void; // Placeholder for booking
}

const PackageDetailsPage: React.FC<PackageDetailsPageProps> = ({ packageData, onBackToPackages, onBookNow }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'itinerary' | 'inclusions' | 'gallery'>('overview');
  const [lightboxImage, setLightboxImage] = useState<string | null>(null);

  if (!packageData) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
        <h2 className="text-2xl font-semibold">Package not found.</h2>
        <button
          onClick={onBackToPackages}
          className="mt-4 bg-brand-orange text-white px-6 py-2 rounded-lg hover:bg-brand-orange-dark transition-colors"
        >
          Back to Packages
        </button>
      </div>
    );
  }

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 0; i < 5; i++) {
      stars.push(
        <StarIcon key={i} className={`w-5 h-5 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`} filled={i < rating} />
      );
    }
    return stars;
  };

  const TabButton: React.FC<{ tabKey: 'overview' | 'itinerary' | 'inclusions' | 'gallery'; label: string }> = ({ tabKey, label }) => (
    <button
      onClick={() => setActiveTab(tabKey)}
      className={`px-4 py-2 font-medium rounded-md transition-colors ${
        activeTab === tabKey ? 'bg-brand-orange text-white' : 'text-brand-gray hover:bg-brand-orange/10 hover:text-brand-orange'
      }`}
      aria-current={activeTab === tabKey ? 'page' : undefined}
    >
      {label}
    </button>
  );

  return (
    <div className="font-poppins">
      {/* Hero Section */}
      <section
        className="relative bg-cover bg-center h-[300px] md:h-[450px]"
        style={{ backgroundImage: `url(${packageData.image})` }}
        aria-labelledby="package-title"
      >
        <div className="absolute inset-0 bg-black/50"></div>
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 h-full flex flex-col justify-end pb-8 md:pb-12">
          <h1 id="package-title" className="text-3xl md:text-5xl font-bold text-white mb-2">{packageData.title}</h1>
          <div className="flex items-center text-gray-200 text-sm md:text-base">
            <LocationMarkerIconOutline className="w-5 h-5 mr-2 text-brand-orange" />
            <span>{packageData.location}</span>
            <span className="mx-2">|</span>
            <span>{packageData.duration}</span>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        <div className="lg:flex lg:gap-8">
          {/* Left Column: Details */}
          <div className="lg:w-2/3">
            <div className="mb-6 p-4 bg-white rounded-lg shadow">
                <p className="text-brand-gray text-base md:text-lg leading-relaxed">{packageData.shortDescription || 'No short description available.'}</p>
            </div>

            {/* Tabs Navigation */}
            <div className="mb-6 flex space-x-2 border-b border-gray-200 pb-2 overflow-x-auto">
              <TabButton tabKey="overview" label="Overview" />
              <TabButton tabKey="itinerary" label="Itinerary" />
              <TabButton tabKey="inclusions" label="Inclusions/Exclusions" />
              <TabButton tabKey="gallery" label="Gallery" />
            </div>

            {/* Tab Content */}
            <div className="bg-white p-6 rounded-lg shadow min-h-[300px]">
              {activeTab === 'overview' && (
                <div>
                  <h3 className="text-2xl font-semibold text-brand-blue-dark mb-4">Package Overview</h3>
                  <p className="text-brand-gray leading-relaxed whitespace-pre-line">
                    {packageData.detailedDescription || 'No detailed description available.'}
                  </p>
                </div>
              )}
              {activeTab === 'itinerary' && (
                <div>
                  <h3 className="text-2xl font-semibold text-brand-blue-dark mb-4">Itinerary Highlights</h3>
                  {packageData.itineraryHighlights && packageData.itineraryHighlights.length > 0 ? (
                    <ul className="list-disc list-inside text-brand-gray space-y-2">
                      {packageData.itineraryHighlights.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-brand-gray">No itinerary highlights available.</p>
                  )}
                </div>
              )}
              {activeTab === 'inclusions' && (
                <div className="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 className="text-xl font-semibold text-green-600 mb-3">What's Included</h3>
                        {packageData.inclusions && packageData.inclusions.length > 0 ? (
                            <ul className="list-disc list-inside text-brand-gray space-y-1">
                            {packageData.inclusions.map((item, index) => (
                                <li key={`inc-${index}`}>{item}</li>
                            ))}
                            </ul>
                        ) : (
                            <p className="text-brand-gray">Details not available.</p>
                        )}
                    </div>
                    <div>
                        <h3 className="text-xl font-semibold text-red-600 mb-3">What's Not Included</h3>
                        {packageData.exclusions && packageData.exclusions.length > 0 ? (
                            <ul className="list-disc list-inside text-brand-gray space-y-1">
                            {packageData.exclusions.map((item, index) => (
                                <li key={`exc-${index}`}>{item}</li>
                            ))}
                            </ul>
                        ) : (
                            <p className="text-brand-gray">Details not available.</p>
                        )}
                    </div>
                </div>
              )}
              {activeTab === 'gallery' && (
                <div>
                  <h3 className="text-2xl font-semibold text-brand-blue-dark mb-4">Image Gallery</h3>
                  {packageData.galleryImages && packageData.galleryImages.length > 0 ? (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                      {packageData.galleryImages.map((imgSrc, index) => (
                        <img
                          key={index}
                          src={imgSrc}
                          alt={`${packageData.title} gallery image ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg shadow-md cursor-pointer hover:opacity-80 transition-opacity"
                          onClick={() => setLightboxImage(imgSrc)}
                        />
                      ))}
                    </div>
                  ) : (
                    <p className="text-brand-gray">No images available for this package.</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Right Column: Booking & Map */}
          <div className="lg:w-1/3 mt-8 lg:mt-0">
            <div className="bg-white p-6 rounded-lg shadow sticky top-24">
              <div className="flex justify-between items-center mb-4">
                <p className="text-3xl font-bold text-brand-orange">${packageData.price}</p>
                <div className="flex">{renderStars(packageData.rating)}</div>
              </div>
              <p className="text-sm text-brand-gray mb-1">Per Person</p>
              <p className="text-sm text-brand-gray mb-4">{packageData.rating.toFixed(1)} rating</p>

              <button
                onClick={() => onBookNow(packageData.id)}
                className="w-full bg-brand-orange text-white px-6 py-3 rounded-lg font-semibold hover:bg-brand-orange-dark transition-colors mb-4 text-lg"
              >
                Book Now
              </button>
              <button
                onClick={onBackToPackages}
                className="w-full bg-gray-200 text-brand-blue-dark px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
              >
                Back to Packages
              </button>

              {packageData.mapEmbedUrl && (
                <div className="mt-6">
                  <h4 className="text-lg font-semibold text-brand-blue-dark mb-2">Location</h4>
                  <iframe
                    src={packageData.mapEmbedUrl}
                    width="100%"
                    height="250"
                    style={{ border: 0 }}
                    allowFullScreen={false}
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Package Location Map"
                    className="rounded-lg"
                  ></iframe>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Lightbox */}
      {lightboxImage && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4 lightbox-backdrop"
          onClick={() => setLightboxImage(null)}
          role="dialog"
          aria-modal="true"
          aria-label="Image Lightbox"
        >
          <div className="relative max-w-5xl max-h-full">
            {/* Close button */}
            <button
              className="absolute -top-16 right-0 bg-white/10 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/20 transition-all duration-300 shadow-lg hover:scale-110"
              onClick={() => setLightboxImage(null)}
              aria-label="Close lightbox"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Image container */}
            <div className="relative bg-white/5 backdrop-blur-md rounded-2xl p-2 shadow-2xl border border-white/10">
              <img
                src={lightboxImage}
                alt="Lightbox view"
                className="max-w-full max-h-full object-contain rounded-xl shadow-lg"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PackageDetailsPage;
