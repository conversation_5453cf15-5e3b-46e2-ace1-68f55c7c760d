
import React from 'react';
import { OUR_PACKAGES_DATA } from '../constants';
import PackageCard from './PackageCard'; 

interface OurPackagesProps {
  onViewDetails: (packageId: string) => void; // Added prop
}

const OurPackages: React.FC<OurPackagesProps> = ({ onViewDetails }) => {
  return (
    <section id="packages" className="py-16 lg:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-brand-blue-dark mb-3 font-poppins">Our Popular Packages</h2>
          <p className="text-lg text-brand-gray max-w-2xl mx-auto font-montserrat">
            Explore our most popular travel packages, curated for unforgettable experiences.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {OUR_PACKAGES_DATA.slice(0, 4).map((pkg) => ( 
            <PackageCard key={pkg.id} packageInfo={pkg} onViewDetails={onViewDetails} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default OurPackages;