
import React from 'react';
import { WHY_CHOOSE_US_FEATURES } from '../constants';
import type { FeatureInfo } from '../types';

// Enhanced Eco-Friendly Feature Card
const FeatureCard: React.FC<{ feature: FeatureInfo }> = ({ feature }) => {
  const Icon = feature.icon; // Assign component to a capitalized variable
  return (
    <div className="eco-card-trust p-8 rounded-2xl shadow-eco-lg flex flex-col items-center text-center group relative overflow-hidden">
      {/* Advanced eco-psychology background pattern with sophisticated opacity */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-forest-light rounded-full blur-xl eco-breathe opacity-eco-breath"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-meadow-spring rounded-full blur-lg eco-breathe opacity-eco-whisper" style={{ animationDelay: '2s' }}></div>

      <div className="relative z-10 p-5 bg-gradient-trust rounded-full text-white mb-6 inline-block shadow-eco group-hover:scale-110 transition-transform duration-300 eco-glow-hover opacity-content-primary">
        <Icon className="w-10 h-10" />
      </div>
      <h3 className="text-xl font-bold text-eco-trust mb-3 font-poppins group-hover:text-forest-trunk transition-colors duration-300 opacity-content-primary eco-fade-in-trust">{feature.title}</h3>
      <p className="text-forest-canopy text-sm font-montserrat leading-relaxed opacity-content-secondary eco-fade-in-calm">{feature.description}</p>

      {/* Sophisticated eco accent with psychology-based opacity */}
      <div className="absolute top-3 right-3 w-2 h-2 bg-water-surface rounded-full eco-pulse-gentle opacity-eco-whisper group-hover:opacity-eco-presence transition-opacity duration-300"></div>
    </div>
  );
};

const WhyChooseUs: React.FC = () => {
  return (
    <section id="about" className="py-16 lg:py-24 eco-section-trust relative overflow-hidden">
      {/* Advanced eco-psychology background elements with sophisticated opacity layering */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-40 h-40 bg-forest-light rounded-full blur-3xl eco-organic-float opacity-atmosphere-far"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-water-surface rounded-full blur-2xl eco-organic-float opacity-atmosphere-mid" style={{ animationDelay: '4s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-28 h-28 bg-meadow-spring rounded-full blur-2xl eco-organic-float opacity-atmosphere-near" style={{ animationDelay: '8s' }}></div>
        <div className="absolute bottom-1/3 left-1/4 w-36 h-36 bg-forest-moss rounded-full blur-3xl eco-organic-float opacity-eco-breath" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/3 right-1/3 w-24 h-24 bg-sunrise-amber rounded-full blur-2xl eco-opacity-wave opacity-eco-whisper" style={{ animationDelay: '6s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-eco-trust mb-4 font-poppins opacity-content-primary eco-fade-in-trust">
            Why Choose Our Eco-Adventures?
          </h2>
          <p className="text-lg text-forest-canopy max-w-2xl mx-auto font-montserrat leading-relaxed opacity-content-secondary eco-fade-in-calm">
            We offer sustainable, tailored travel experiences that protect our planet while ensuring every journey is unique, memorable, and environmentally transformative.
          </p>
        </div>
        {/* The type error for WHY_CHOOSE_US_FEATURES on this line (previously line 28)
            is resolved by correcting the FeatureInfo.icon type in types.ts */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {WHY_CHOOSE_US_FEATURES.map((feature, index) => (
            <FeatureCard key={index} feature={feature} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;