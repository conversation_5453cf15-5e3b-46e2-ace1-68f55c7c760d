import React from 'react';

const MapSection: React.FC = () => {
  // Replace with your actual Google Maps embed URL
  const mapEmbedUrl = "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3445.180600588068!2d-97.7451402848785!3d30.28899098179377!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x8644b5817ef9940d%3A0x57a398998fcfd268!2sTexas%20State%20Capitol!5e0!3m2!1sen!2sus!4v1628500000000!5m2!1sen!2sus";

  return (
    <section className="py-16 lg:py-20 bg-brand-gray-light">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-brand-blue-dark mb-3 font-poppins">Find Us On Map</h2>
          <p className="text-lg text-brand-gray max-w-xl mx-auto font-montserrat">
            Visit our office or get directions easily.
          </p>
        </div>
        <div className="aspect-w-16 aspect-h-9 rounded-xl overflow-hidden shadow-xl">
          <iframe
            src={mapEmbedUrl}
            width="100%"
            height="450"
            style={{ border: 0 }}
            allowFullScreen={false}
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            title="Rebel Rover Location"
            aria-label="Google Map showing Rebel Rover location"
          ></iframe>
        </div>
      </div>
    </section>
  );
};

export default MapSection;