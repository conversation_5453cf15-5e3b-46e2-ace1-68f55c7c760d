
import React, { useRef, useState, useEffect } from 'react';
import { FEATURED_DESTINATIONS_DATA } from '../constants';
import type { Destination } from '../types';

const DestinationCard: React.FC<{ destination: Destination }> = ({ destination }) => (
  <div className="relative rounded-xl overflow-hidden shadow-lg group aspect-[3/4] flex-shrink-0 w-80 sm:w-96 destination-card destination-card-enter">
    <img src={destination.image} alt={destination.name} className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" />
    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
    <div className="absolute bottom-0 left-0 right-0 p-6">
      <h3 className="text-2xl font-bold text-white font-poppins">{destination.name}</h3>
    </div>
    <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
  </div>
);

const FeaturedDestinations: React.FC = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isAutoScrolling, setIsAutoScrolling] = useState(true);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const autoScrollIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Duplicate destinations for seamless infinite scroll
  const duplicatedDestinations = [...FEATURED_DESTINATIONS_DATA, ...FEATURED_DESTINATIONS_DATA];

  // Check scroll position to update navigation buttons
  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      const isAtStart = scrollLeft <= 5;
      const isAtEnd = scrollLeft >= scrollWidth - clientWidth - 5;

      setCanScrollLeft(!isAtStart);
      setCanScrollRight(!isAtEnd);
    }
  };

  // Auto scroll function
  const autoScroll = () => {
    if (scrollContainerRef.current && isAutoScrolling) {
      const container = scrollContainerRef.current;
      const cardWidth = 400; // Card width + gap
      const maxScroll = container.scrollWidth - container.clientWidth;

      // If we've reached the end, reset to beginning for infinite scroll
      if (container.scrollLeft >= maxScroll - 10) {
        container.scrollTo({ left: 0, behavior: 'smooth' });
      } else {
        container.scrollBy({ left: cardWidth, behavior: 'smooth' });
      }
    }
  };

  // Manual scroll functions
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -400,
        behavior: 'smooth'
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 400,
        behavior: 'smooth'
      });
    }
  };

  // Handle mouse enter/leave for auto-scroll pause
  const handleMouseEnter = () => {
    setIsAutoScrolling(false);
  };

  const handleMouseLeave = () => {
    setIsAutoScrolling(true);
  };

  // Set up auto-scroll interval
  useEffect(() => {
    if (isAutoScrolling) {
      autoScrollIntervalRef.current = setInterval(autoScroll, 3000); // Auto scroll every 3 seconds
    } else {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
        autoScrollIntervalRef.current = null;
      }
    }

    return () => {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
    };
  }, [isAutoScrolling]);

  // Check scroll position on mount and resize
  useEffect(() => {
    const timer = setTimeout(() => {
      checkScrollPosition();
    }, 100);

    const handleResize = () => checkScrollPosition();
    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <section id="destinations" className="py-16 lg:py-24 bg-brand-gray-light">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-brand-blue-dark mb-3 font-poppins">Featured Destinations</h2>
          <p className="text-lg text-brand-gray max-w-2xl mx-auto font-montserrat">
            Discover breathtaking places around the world, handpicked for you.
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Navigation Arrows */}
          <button
            onClick={scrollLeft}
            disabled={!canScrollLeft}
            className={`absolute left-2 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white shadow-lg flex items-center justify-center transition-all duration-300 border-2 ${
              canScrollLeft
                ? 'text-brand-blue-dark hover:bg-brand-orange hover:text-white hover:border-brand-orange border-eco-green-light'
                : 'text-gray-400 cursor-not-allowed border-brand-gray-light opacity-50'
            }`}
            aria-label="Scroll left"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={scrollRight}
            disabled={!canScrollRight}
            className={`absolute right-2 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white shadow-lg flex items-center justify-center transition-all duration-300 border-2 ${
              canScrollRight
                ? 'text-brand-blue-dark hover:bg-brand-orange hover:text-white hover:border-brand-orange border-eco-green-light'
                : 'text-gray-400 cursor-not-allowed border-brand-gray-light opacity-50'
            }`}
            aria-label="Scroll right"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>

          {/* Auto-scroll pause/play indicator */}
          <div className="absolute top-4 right-4 z-10">
            <div className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 auto-scroll-indicator ${
              isAutoScrolling
                ? 'bg-eco-green text-white'
                : 'bg-brand-orange text-white'
            }`}>
              {isAutoScrolling ? 'Auto-scrolling' : 'Paused'}
            </div>
          </div>

          {/* Scrollable Destinations */}
          <div
            ref={scrollContainerRef}
            onScroll={checkScrollPosition}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className="flex gap-6 overflow-x-auto pb-4 px-16 scrollbar-hide auto-scroll-container"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
            }}
          >
            {duplicatedDestinations.map((destination, index) => (
              <DestinationCard
                key={`${destination.id}-${index}`}
                destination={destination}
              />
            ))}
          </div>

          {/* Auto-scroll controls */}
          <div className="flex justify-center mt-6 gap-4">
            <button
              onClick={() => setIsAutoScrolling(!isAutoScrolling)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                isAutoScrolling
                  ? 'bg-brand-orange text-white hover:bg-brand-orange-dark'
                  : 'bg-eco-green text-white hover:bg-eco-green-dark'
              }`}
            >
              {isAutoScrolling ? 'Pause Auto-scroll' : 'Resume Auto-scroll'}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedDestinations;