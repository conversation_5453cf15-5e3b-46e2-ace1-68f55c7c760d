
import React from 'react';
import PackagesHero from '../components/PackagesHero';
import PackageCard from '../components/PackageCard';
import { OUR_PACKAGES_DATA } from '../constants';
import Subscribe from '../components/Subscribe';

interface PackagesPageProps {
  onViewDetails: (packageId: string) => void; // Added prop
}

const PackagesPage: React.FC<PackagesPageProps> = ({ onViewDetails }) => {
  return (
    <>
      <PackagesHero />
      <section className="py-16 lg:py-24 bg-brand-gray-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-brand-blue-dark mb-3 font-poppins">
              Explore All Our Packages
            </h2>
            <p className="text-lg text-brand-gray max-w-2xl mx-auto font-montserrat">
              Find the perfect adventure from our wide selection of curated travel deals. 
              Each package is designed to offer unique experiences and create lasting memories.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {OUR_PACKAGES_DATA.map((pkg) => (
              <PackageCard key={pkg.id} packageInfo={pkg} onViewDetails={onViewDetails} />
            ))}
          </div>
        </div>
      </section>
      <Subscribe />
    </>
  );
};

export default PackagesPage;