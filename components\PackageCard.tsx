
import React from 'react';
import type { PackageDeal } from '../types';
import { StarIcon } from './icons/StarIcon';
import { LocationMarkerIconOutline } from './icons/HeroIcons';

interface PackageCardProps {
  packageInfo: PackageDeal;
  onViewDetails: (packageId: string) => void;
}

const PackageCard: React.FC<PackageCardProps> = ({ packageInfo, onViewDetails }) => {
  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 !== 0;
    const starsArray = [];
    for (let i = 0; i < fullStars; i++) {
      starsArray.push(<StarIcon key={`full-${i}`} className="w-5 h-5 text-yellow-400" filled />);
    }
    if (halfStar) {
      starsArray.push(<StarIcon key="half" className="w-5 h-5 text-yellow-400" filled />);
    }
    return starsArray;
  };

  const handleViewDetailsClick = () => {
    if (typeof onViewDetails === 'function') {
      onViewDetails(packageInfo.id);
    } else {
      console.error(
        "PackageCard Error: onViewDetails prop is not a function. Received:",
        onViewDetails,
        "Type:", typeof onViewDetails,
        "Package ID:", packageInfo.id
      );
      alert("Sorry, details for this package cannot be viewed at the moment.");
    }
  };

  return (
    <div className="eco-card-growth rounded-2xl shadow-eco-lg overflow-hidden group transition-all duration-500 flex flex-col h-full relative">
      {/* Advanced eco-psychology background accents */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-forest-light/12 rounded-full blur-xl eco-breathe"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-meadow-spring/10 rounded-full blur-lg eco-breathe" style={{ animationDelay: '3s' }}></div>

      <div className="relative overflow-hidden">
        <img
          src={packageInfo.image}
          alt={packageInfo.title}
          className="w-full h-56 object-cover transition-transform duration-500 group-hover:scale-110 group-hover:brightness-110"
        />
        {/* Sophisticated eco overlay with gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-forest-trunk/25 via-transparent to-water-surface/15 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Enhanced eco corner accent with gentle pulse */}
        <div className="absolute top-3 right-3 w-3 h-3 bg-water-surface rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300 eco-pulse-gentle"></div>
      </div>

      <div className="p-6 flex flex-col flex-grow relative z-10">
        <div className="flex items-center text-forest-canopy text-sm mb-2">
          <LocationMarkerIconOutline className="w-4 h-4 mr-1 text-forest-trunk" />
          {packageInfo.location}
        </div>
        <h3 className="text-xl font-bold text-eco-growth mb-3 font-poppins flex-grow group-hover:text-forest-trunk transition-colors duration-300">{packageInfo.title}</h3>
        <div className="flex items-center mb-3">
          {renderStars(packageInfo.rating)}
          <span className="ml-2 text-sm text-forest-canopy">({packageInfo.rating.toFixed(1)})</span>
        </div>
        <p className="text-sm text-forest-canopy mb-4 bg-water-surface/15 px-3 py-1 rounded-full inline-block border border-water-surface/20">{packageInfo.duration}</p>
        <div className="flex justify-between items-center mt-auto">
          <p className="text-2xl font-bold text-eco-energy">${packageInfo.price}</p>
          <button
            className="eco-btn-action px-6 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300"
            aria-label={`View details for ${packageInfo.title}`}
            onClick={handleViewDetailsClick}
          >
            🌿 View Details
          </button>
        </div>
      </div>
    </div>
  );
};

export default PackageCard;
