import React from 'react';
import Hero from '../components/Hero';
import WhyChooseUs from '../components/WhyChooseUs';
import OurPackages from '../components/OurPackages';
import OurServices from '../components/OurServices';
import FeaturedDestinations from '../components/FeaturedDestinations';
import Testimonials from '../components/Testimonials';
import Subscribe from '../components/Subscribe';

interface HomePageProps {
  onViewPackageDetails: (packageId: string) => void; // Added prop
}

const HomePage: React.FC<HomePageProps> = ({ onViewPackageDetails }) => {
  return (
    <>
      <Hero />
      <WhyChooseUs />
      <OurPackages onViewDetails={onViewPackageDetails} />
      <OurServices />
      <FeaturedDestinations />
      <Testimonials />
      {/* <Subscribe /> */}
    </>
  );
};

export default HomePage;