import React from 'react';
import { OUR_SERVICES_DATA } from '../constants';
import type { ServiceInfo } from '../types';

const ServiceCard: React.FC<{ service: ServiceInfo }> = ({ service }) => {
  const IconComponent = service.icon;
  return (
    <div className="eco-card rounded-2xl shadow-eco-lg overflow-hidden group transition-all duration-500 ease-in-out flex flex-col h-full relative">
      {/* Eco-friendly background accents */}
      <div className="absolute top-0 right-0 w-16 h-16 bg-brand-mint/10 rounded-full blur-lg"></div>
      <div className="absolute bottom-0 left-0 w-12 h-12 bg-brand-green/10 rounded-full blur-md"></div>

      <div className="w-full h-48 overflow-hidden rounded-t-2xl relative">
        <img
          src={service.image}
          alt={service.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110 group-hover:brightness-110"
        />
        {/* Eco overlay on image */}
        <div className="absolute inset-0 bg-gradient-to-t from-brand-teal/20 via-transparent to-brand-mint/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>
      <div className="p-6 pt-0 flex flex-col items-center text-center flex-grow relative z-10">
        <div className="p-4 bg-gradient-primary rounded-full text-white mb-4 inline-block transform -translate-y-1/2 border-4 border-white group-hover:scale-110 transition-all duration-300 shadow-eco-lg eco-glow-hover">
          <IconComponent className="w-6 h-6" />
        </div>
        <h3 className="text-xl font-bold text-eco-primary mb-3 font-poppins -mt-4 group-hover:text-brand-teal transition-colors duration-300">{service.title}</h3>
        <p className="text-brand-gray-dark text-sm font-montserrat flex-grow leading-relaxed">{service.description}</p>

        {/* Eco accent dot */}
        <div className="absolute top-3 right-3 w-2 h-2 bg-brand-mint rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>
    </div>
  );
};

const OurServices: React.FC = () => {
  return (
    <section id="services" className="py-16 lg:py-24 bg-white relative overflow-hidden">
      {/* Eco-friendly background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-16 right-16 w-36 h-36 bg-brand-teal rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-16 left-16 w-28 h-28 bg-brand-mint rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-brand-green rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-eco-primary mb-4 font-poppins">
            Our Eco-Friendly Services
          </h2>
          <p className="text-lg text-brand-gray-dark max-w-2xl mx-auto font-montserrat leading-relaxed">
            Comprehensive sustainable travel solutions to make your eco-conscious journey seamless, enjoyable, and environmentally responsible.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {OUR_SERVICES_DATA.map((service, index) => (
            <ServiceCard key={index} service={service} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default OurServices;