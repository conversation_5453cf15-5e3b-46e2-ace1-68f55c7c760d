import React from 'react';

const BlogHero: React.FC = () => {
  return (
    <section
      className="relative bg-cover bg-center h-[350px] md:h-[400px] flex items-center justify-center text-white"
      style={{ backgroundImage: "url('https://picsum.photos/seed/blog-hero/1920/800')" }}
      aria-labelledby="blog-hero-title"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-brand-teal/70 via-brand-teal-dark/60 to-brand-mint/50"></div>
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1
          id="blog-hero-title"
          className="text-3xl md:text-4xl lg:text-5xl font-bold font-poppins mb-4"
        >
          Travel <span className="text-brand-green">Stories</span>
        </h1>
        <p className="text-base md:text-lg font-montserrat max-w-2xl mx-auto opacity-90">
          Discover inspiring travel stories, expert tips, and Sri Lanka's hidden gems.
        </p>
      </div>
    </section>
  );
};

export default BlogHero;
