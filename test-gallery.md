# AboutGallery Component Test Guide

## Features to Test

### 1. **Image Carousel Scrolling**
- ✅ Horizontal scroll with mouse wheel
- ✅ Touch/swipe scrolling on mobile devices
- ✅ Navigation arrows (left/right buttons)
- ✅ Smooth scroll behavior

### 2. **Navigation Controls**
- ✅ Left arrow button (disabled when at start)
- ✅ Right arrow button (disabled when at end)
- ✅ Hover effects on navigation buttons
- ✅ Button state changes based on scroll position

### 3. **Image Display**
- ✅ 16 gallery images loaded from constants
- ✅ Lazy loading for performance
- ✅ Hover effects (scale animation)
- ✅ Responsive image sizing (400px x 400px - increased size)

### 4. **Lightbox Functionality**
- ✅ Click any image to open lightbox
- ✅ Full-screen image display
- ✅ Close button in top-right corner
- ✅ Click outside image to close
- ✅ ESC key to close
- ✅ Background blur effect
- ✅ Prevent body scrolling when open

### 5. **Responsive Design**
- ✅ Works on desktop, tablet, and mobile
- ✅ Touch-friendly navigation
- ✅ Proper spacing and layout

### 6. **Accessibility**
- ✅ Proper ARIA labels
- ✅ Keyboard navigation support
- ✅ Alt text for all images
- ✅ Focus management

## How to Test

1. **Navigate to About Page**: Click "About Us" in the navigation
2. **Scroll the Gallery**:
   - Use mouse wheel over the gallery
   - Click and drag to scroll
   - Use navigation arrows
3. **Test Lightbox**: Click any image to open full-screen view
4. **Test Mobile**: Resize browser or use mobile device
5. **Test Keyboard**: Use Tab, Enter, and ESC keys

## Expected Behavior

- Gallery should smoothly scroll horizontally
- Images should have hover effects
- Lightbox should open/close properly
- Navigation arrows should enable/disable correctly
- All interactions should be smooth and responsive

## Performance Notes

- Images use lazy loading
- Smooth scroll behavior implemented
- Optimized for touch devices
- Hidden scrollbars for clean appearance
