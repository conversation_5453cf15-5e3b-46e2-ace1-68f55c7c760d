
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rebel Rover</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="index.css">
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              poppins: ['Poppins', 'sans-serif'],
              montserrat: ['Montserrat', 'sans-serif'],
            },
            colors: {
              // 🌿 ADVANCED ECO-PSYCHOLOGY COLOR PALETTE
              // Based on biophilic design principles and color psychology research

              // === FOREST CANOPY SERIES (Primary Trust & Growth) ===
              'forest-deep': '#1B4332',         // Deep forest shadow - trust, stability, grounding
              'forest-canopy': '#2D5A3D',       // Forest canopy - security, protection, wisdom
              'forest-trunk': '#3D8D7A',        // Tree trunk - strength, reliability, growth
              'forest-moss': '#52B788',         // Fresh moss - renewal, vitality, hope
              'forest-light': '#74C69D',        // Filtered sunlight - optimism, freshness, life

              // === MEADOW SERIES (Secondary Harmony & Peace) ===
              'meadow-sage': '#95D5B2',         // Sage grass - tranquility, balance, wisdom
              'meadow-spring': '#B7E4C7',       // Spring meadow - growth, harmony, peace
              'meadow-bloom': '#D8F3DC',        // Flower bloom - purity, new beginnings, hope

              // === EARTH SERIES (Grounding & Warmth) ===
              'earth-rich': '#8B5A3C',          // Rich soil - grounding, nourishment, stability
              'earth-clay': '#A0785A',          // Clay earth - warmth, comfort, natural
              'earth-sand': '#D4A574',          // Sandy earth - approachability, warmth

              // === SKY SERIES (Inspiration & Freedom) ===
              'sky-storm': '#2C5F7C',           // Storm clouds - power, depth, mystery
              'sky-clear': '#4A90A4',           // Clear sky - clarity, freedom, possibility
              'sky-dawn': '#7FB3D3',            // Dawn sky - hope, new beginnings, inspiration

              // === WATER SERIES (Flow & Purity) ===
              'water-deep': '#1D3557',          // Deep water - depth, mystery, trust
              'water-current': '#457B9D',       // Flowing water - movement, progress, life
              'water-surface': '#A8DADC',       // Water surface - clarity, reflection, peace
              'water-mist': '#F1FAEE',          // Water mist - purity, lightness, freshness

              // === SUNRISE SERIES (Energy & Optimism) ===
              'sunrise-gold': '#F9C74F',        // Golden sunrise - energy, optimism, warmth
              'sunrise-amber': '#F8961E',       // Amber glow - enthusiasm, creativity, joy
              'sunrise-coral': '#F3722C',       // Coral dawn - passion, adventure, courage

              // === NEUTRAL EARTH TONES (Balance & Sophistication) ===
              'neutral-stone': '#E9EDC9',       // Light stone - calm, sophisticated, clean
              'neutral-pebble': '#CCD5AE',      // River pebble - balanced, natural, serene
              'neutral-bark': '#FEFAE0',        // Tree bark - organic, warm, inviting
              'neutral-shadow': '#FAEDCD',      // Soft shadow - gentle, subtle, elegant

              // === LEGACY MAPPINGS (Backward Compatibility) ===
              'brand-teal': '#3D8D7A',          // Maps to forest-trunk
              'brand-teal-dark': '#2D5A3D',     // Maps to forest-canopy
              'brand-teal-light': '#52B788',    // Maps to forest-moss
              'brand-green': '#B7E4C7',         // Maps to meadow-spring
              'brand-green-dark': '#95D5B2',    // Maps to meadow-sage
              'brand-green-light': '#D8F3DC',   // Maps to meadow-bloom
              'brand-cream': '#FEFAE0',         // Maps to neutral-bark
              'brand-cream-dark': '#FAEDCD',    // Maps to neutral-shadow
              'brand-mint': '#A8DADC',          // Maps to water-surface
              'brand-mint-dark': '#457B9D',     // Maps to water-current
              'brand-mint-light': '#F1FAEE',    // Maps to water-mist
              'brand-gray': '#8B5A3C',          // Maps to earth-rich
              'brand-gray-light': '#E9EDC9',    // Maps to neutral-stone
              'brand-gray-medium': '#CCD5AE',   // Maps to neutral-pebble
              'brand-gray-dark': '#1B4332',     // Maps to forest-deep

              // Legacy orange/blue mappings
              'brand-orange': '#F8961E',        // Maps to sunrise-amber
              'brand-orange-dark': '#F3722C',   // Maps to sunrise-coral
              'brand-orange-light': '#F9C74F',  // Maps to sunrise-gold
              'brand-blue-dark': '#1D3557',     // Maps to water-deep
              'brand-blue-light': '#4A90A4',    // Maps to sky-clear
              'brand-blue': '#2C5F7C',          // Maps to sky-storm

              // Eco-specific colors
              'eco-green': '#74C69D',           // Maps to forest-light
              'eco-green-light': '#D8F3DC',     // Maps to meadow-bloom
              'eco-green-dark': '#52B788',      // Maps to forest-moss
              'eco-sage': '#95D5B2',            // Maps to meadow-sage

              // Status colors with eco-psychology
              'success': '#74C69D',             // Forest-light - growth, achievement
              'warning': '#F9C74F',             // Sunrise-gold - attention, caution
              'error': '#F3722C',               // Sunrise-coral - urgency, action needed
              'info': '#4A90A4',                // Sky-clear - information, clarity
            },
            backgroundImage: {
              'hero-pattern': "url('https://picsum.photos/seed/nature-forest/1920/1080')",
              'subscribe-pattern': "url('https://picsum.photos/seed/nature-mountains/1600/500')",

              // 🌿 ADVANCED ECO-PSYCHOLOGY GRADIENTS
              // Primary gradients for trust and growth
              'gradient-primary': 'linear-gradient(135deg, #3D8D7A 0%, #2D5A3D 100%)',           // Forest trunk to canopy
              'gradient-secondary': 'linear-gradient(135deg, #B7E4C7 0%, #95D5B2 100%)',         // Meadow spring to sage
              'gradient-forest': 'linear-gradient(135deg, #1B4332 0%, #3D8D7A 50%, #74C69D 100%)', // Deep forest to light
              'gradient-meadow': 'linear-gradient(135deg, #D8F3DC 0%, #B7E4C7 50%, #95D5B2 100%)', // Bloom to sage
              'gradient-water': 'linear-gradient(135deg, #1D3557 0%, #457B9D 50%, #A8DADC 100%)', // Deep water to surface
              'gradient-sunrise': 'linear-gradient(135deg, #F9C74F 0%, #F8961E 50%, #F3722C 100%)', // Gold to coral
              'gradient-earth': 'linear-gradient(135deg, #8B5A3C 0%, #A0785A 50%, #D4A574 100%)', // Rich soil to sand
              'gradient-sky': 'linear-gradient(135deg, #2C5F7C 0%, #4A90A4 50%, #7FB3D3 100%)', // Storm to dawn

              // Card and section gradients
              'gradient-card': 'linear-gradient(145deg, #FEFAE0 0%, #FAEDCD 100%)',              // Bark to shadow
              'gradient-card-hover': 'linear-gradient(145deg, #FAEDCD 0%, #FEFAE0 100%)',       // Reverse for hover
              'gradient-section-light': 'linear-gradient(135deg, #FEFAE0 0%, #E9EDC9 100%)',    // Bark to stone
              'gradient-section-dark': 'linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%)',     // Deep forest to canopy

              // Specialty gradients for specific moods
              'gradient-trust': 'linear-gradient(135deg, #2D5A3D 0%, #3D8D7A 100%)',            // Canopy to trunk
              'gradient-growth': 'linear-gradient(135deg, #52B788 0%, #74C69D 100%)',           // Moss to light
              'gradient-harmony': 'linear-gradient(135deg, #95D5B2 0%, #B7E4C7 100%)',          // Sage to spring
              'gradient-purity': 'linear-gradient(135deg, #F1FAEE 0%, #A8DADC 100%)',           // Mist to surface
              'gradient-energy': 'linear-gradient(135deg, #F9C74F 0%, #F8961E 100%)',           // Gold to amber
              'gradient-warmth': 'linear-gradient(135deg, #A0785A 0%, #D4A574 100%)',           // Clay to sand

              // Overlay gradients for depth and atmosphere
              'gradient-overlay-forest': 'linear-gradient(to bottom, rgba(27, 67, 50, 0.8) 0%, rgba(61, 141, 122, 0.6) 50%, rgba(116, 198, 157, 0.4) 100%)',
              'gradient-overlay-water': 'linear-gradient(to bottom, rgba(29, 53, 87, 0.8) 0%, rgba(69, 123, 157, 0.6) 50%, rgba(168, 218, 220, 0.4) 100%)',
              'gradient-overlay-sunrise': 'linear-gradient(to bottom, rgba(249, 199, 79, 0.8) 0%, rgba(248, 150, 30, 0.6) 50%, rgba(243, 114, 44, 0.4) 100%)',

              // Legacy compatibility
              'gradient-mint': 'linear-gradient(135deg, #A8DADC 0%, #457B9D 100%)',             // Water surface to current
              'gradient-cream': 'linear-gradient(135deg, #FEFAE0 0%, #E9EDC9 100%)',           // Bark to stone
              'gradient-hero': 'linear-gradient(135deg, #1B4332 0%, #3D8D7A 50%, #A8DADC 100%)', // Deep forest to water
            }
          }
        }
      }
    </script>
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/"
  }
}
</script>
</head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
