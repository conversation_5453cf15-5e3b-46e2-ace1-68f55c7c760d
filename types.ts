export interface NavLink {
  href: string;
  label: string;
  isPage?: boolean; // Added for page navigation
}

export interface FeatureInfo {
  // Corrected: icon should be a component type, not just any ReactNode, to be renderable as <Icon />
  icon: React.ElementType;
  title: string;
  description: string;
}

export interface ServiceInfo {
  icon: React.ElementType;
  title: string;
  description: string;
  image: string; // Added image URL for each service
}

export interface PackageDeal {
  id: string;
  image: string;
  location: string;
  price: number;
  rating: number;
  duration: string;
  title: string;
  shortDescription?: string; // For details page
  detailedDescription?: string; // For details page
  itineraryHighlights?: string[]; // For details page
  inclusions?: string[]; // For details page
  exclusions?: string[]; // For details page
  galleryImages?: string[]; // For details page
  mapEmbedUrl?: string; // Optional for details page
}

export interface Destination {
  id: string;
  name: string;
  image: string;
}

export interface Testimonial {
  id: string;
  avatar: string;
  name: string;
  review: string;
  rating: number;
}

export interface GalleryImage {
  id: string;
  src: string;
  alt: string;
}

export interface ClientLogo {
  id: string;
  src: string;
  alt: string;
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  image: string;
  author: {
    name: string;
    avatar: string;
  };
  category: string;
  tags: string[];
  publishedDate: string;
  readTime: number; // in minutes
  featured?: boolean;
}