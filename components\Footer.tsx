
import React from 'react';
import { FacebookIcon, InstagramIcon, TwitterIcon, LinkedinIcon } from './icons/SocialIcons';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    company: [
      { label: 'About Us', href: '#' },
      { label: 'Our Team', href: '#' },
      { label: 'Careers', href: '#' },
      { label: 'Blog', href: '#' },
    ],
    support: [
      { label: 'FAQ', href: '#' },
      { label: 'Help Center', href: '#' },
      { label: 'Contact Us', href: '#' },
      { label: 'Terms of Service', href: '#' },
    ],
    destinations: [
      { label: 'Europe', href: '#' },
      { label: 'Asia', href: '#' },
      { label: 'North America', href: '#' },
      { label: 'South America', href: '#' },
    ],
  };

  return (
    <footer id="contact" className="eco-section-dark text-gray-300 py-16 lg:py-20 font-montserrat relative overflow-hidden">
      {/* Eco-friendly background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-16 left-16 w-32 h-32 bg-brand-mint rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-16 right-16 w-28 h-28 bg-brand-green rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-brand-teal rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          {/* Column 1: Logo and About */}
          <div>
            <a href="#home" className="text-3xl font-bold text-white font-poppins mb-4 inline-block">
              Shan <span className="text-brand-green">Travels</span>
            </a>
            <p className="text-sm leading-relaxed mb-6 text-gray-300">
              Your ultimate partner for sustainable travel. We provide exceptional eco-friendly travel experiences that protect our planet while creating unforgettable memories.
            </p>
            <div className="flex space-x-3">
              <a
                href="#"
                className="footer-social-icon group relative p-3 rounded-xl bg-gradient-trust text-white hover:bg-gradient-sky transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 shadow-eco hover:shadow-eco-xl border border-white/20 hover:border-white/40 backdrop-blur-sm social-icon-enhanced social-pulse-glow"
                aria-label="Follow us on Facebook"
              >
                <FacebookIcon className="w-6 h-6 relative z-10 drop-shadow-lg group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out rounded-xl"></div>
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-sunrise-gold rounded-full opacity-80 animate-pulse"></div>
              </a>

              <a
                href="#"
                className="footer-social-icon group relative p-3 rounded-xl bg-gradient-sunrise text-white hover:bg-gradient-energy transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 shadow-eco hover:shadow-eco-xl border border-white/20 hover:border-white/40 backdrop-blur-sm social-icon-enhanced"
                aria-label="Follow us on Instagram"
              >
                <InstagramIcon className="w-6 h-6 relative z-10 drop-shadow-lg group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out rounded-xl"></div>
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-water-surface rounded-full opacity-80 animate-pulse"></div>
              </a>

              <a
                href="#"
                className="footer-social-icon group relative p-3 rounded-xl bg-gradient-sky text-white hover:bg-gradient-water transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 shadow-eco hover:shadow-eco-xl border border-white/20 hover:border-white/40 backdrop-blur-sm social-icon-enhanced"
                aria-label="Follow us on Twitter"
              >
                <TwitterIcon className="w-6 h-6 relative z-10 drop-shadow-lg group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out rounded-xl"></div>
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-forest-light rounded-full opacity-80 animate-pulse"></div>
              </a>

              <a
                href="#"
                className="footer-social-icon group relative p-3 rounded-xl bg-gradient-forest text-white hover:bg-gradient-growth transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 shadow-eco hover:shadow-eco-xl border border-white/20 hover:border-white/40 backdrop-blur-sm social-icon-enhanced"
                aria-label="Connect with us on LinkedIn"
              >
                <LinkedinIcon className="w-6 h-6 relative z-10 drop-shadow-lg group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out rounded-xl"></div>
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-meadow-spring rounded-full opacity-80 animate-pulse"></div>
              </a>
            </div>
          </div>

          {/* Column 2: Company Links */}
          <div>
            <h5 className="text-xl font-semibold text-white font-poppins mb-6">Company</h5>
            <ul className="space-y-3">
              {footerLinks.company.map(link => (
                <li key={link.label}><a href={link.href} className="hover:text-brand-green transition-colors duration-300">{link.label}</a></li>
              ))}
            </ul>
          </div>

          {/* Column 3: Support Links */}
          <div>
            <h5 className="text-xl font-semibold text-white font-poppins mb-6">Support</h5>
            <ul className="space-y-3">
              {footerLinks.support.map(link => (
                <li key={link.label}><a href={link.href} className="hover:text-brand-green transition-colors duration-300">{link.label}</a></li>
              ))}
            </ul>
          </div>

          {/* Column 4: Top Destinations (or contact info) */}
           <div>
            <h5 className="text-xl font-semibold text-white font-poppins mb-6">Contact Info</h5>
            <ul className="space-y-3">
                <li><span className="font-semibold">Address:</span> 123 Travel Lane, Wanderlust City, TX 78701</li>
                <li><span className="font-semibold">Phone:</span> (*************</li>
                <li><span className="font-semibold">Email:</span> <EMAIL></li>
            </ul>
          </div>

        </div>

        <div className="border-t border-gray-700 pt-8 text-center text-sm">
          <p>&copy; {currentYear} Rebel Rover. All Rights Reserved. Crafted with passion.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
