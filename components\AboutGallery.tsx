import React, { useRef, useState, useEffect } from 'react';
import { ABOUT_GALLERY_IMAGES } from '../constants';
import type { GalleryImage } from '../types';

const AboutGallery: React.FC = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false); // Start with false, will be updated after mount
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [scrollProgress, setScrollProgress] = useState(0);

  // Check scroll position to update navigation buttons and indicators
  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      const isAtStart = scrollLeft <= 5; // Small tolerance for floating point precision
      const isAtEnd = scrollLeft >= scrollWidth - clientWidth - 5;

      setCanScrollLeft(!isAtStart);
      setCanScrollRight(!isAtEnd);

      // Calculate scroll progress (0 to 1)
      const maxScroll = scrollWidth - clientWidth;
      const progress = maxScroll > 0 ? scrollLeft / maxScroll : 0;
      setScrollProgress(progress);
    }
  };

  // Scroll functions
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -420, // Scroll by approximately one image width (400px + gap)
        behavior: 'smooth'
      });
      // Check scroll position after animation completes
      setTimeout(checkScrollPosition, 300);
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 420, // Scroll by approximately one image width (400px + gap)
        behavior: 'smooth'
      });
      // Check scroll position after animation completes
      setTimeout(checkScrollPosition, 300);
    }
  };

  // Handle image click for lightbox
  const openLightbox = (imageSrc: string) => {
    setSelectedImage(imageSrc);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  // Handle keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeLightbox();
      }
    };

    if (selectedImage) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [selectedImage]);

  // Check scroll position on mount and resize
  useEffect(() => {
    // Use a timeout to ensure images are loaded and container has proper dimensions
    const timer = setTimeout(() => {
      checkScrollPosition();
    }, 100);

    const handleResize = () => checkScrollPosition();
    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <section className="py-16 lg:py-24 bg-gradient-cream relative overflow-hidden">
      {/* Subtle eco-friendly background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 bg-brand-teal rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-brand-mint rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-brand-green rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/4 right-1/4 w-20 h-20 bg-brand-green-light rounded-full blur-2xl animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-1/3 left-1/4 w-28 h-28 bg-brand-mint-light rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold mb-3 font-poppins text-gradient-primary">
            Gallery
          </h2>
          <p className="text-lg text-brand-gray-dark max-w-2xl mx-auto font-montserrat leading-relaxed">
            Explore our journey and the beautiful moments captured along the way through our eco-conscious adventures.
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Navigation Arrows */}
          <button
            onClick={scrollLeft}
            disabled={!canScrollLeft}
            style={{
              position: 'absolute',
              left: '8px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 50,
              cursor: 'pointer',
              pointerEvents: 'auto'
            }}
            className={`w-16 h-16 rounded-full shadow-eco-lg flex items-center justify-center transition-all duration-300 border-2 backdrop-blur-sm ${
              canScrollLeft
                ? 'bg-brand-cream/90 text-brand-teal hover:bg-gradient-primary hover:text-white hover:border-brand-teal hover:scale-110 border-brand-mint shadow-mint'
                : 'bg-brand-gray-light/60 text-brand-gray cursor-not-allowed border-brand-gray-medium opacity-60'
            }`}
            aria-label="Scroll left"
          >
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={scrollRight}
            disabled={!canScrollRight}
            style={{
              position: 'absolute',
              right: '8px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 50,
              cursor: 'pointer',
              pointerEvents: 'auto'
            }}
            className={`w-16 h-16 rounded-full shadow-eco-lg flex items-center justify-center transition-all duration-300 border-2 backdrop-blur-sm ${
              canScrollRight
                ? 'bg-brand-cream/90 text-brand-teal hover:bg-gradient-primary hover:text-white hover:border-brand-teal hover:scale-110 border-brand-mint shadow-mint'
                : 'bg-brand-gray-light/60 text-brand-gray cursor-not-allowed border-brand-gray-medium opacity-60'
            }`}
            aria-label="Scroll right"
          >
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M9 5l7 7-7 7" />
            </svg>
          </button>

          {/* Scrollable Gallery */}
          <div
            ref={scrollContainerRef}
            onScroll={checkScrollPosition}
            className="flex gap-6 overflow-x-auto pb-4 px-20 scrollbar-hide"
            style={{
              scrollbarWidth: 'none', // Firefox
              msOverflowStyle: 'none', // IE/Edge
            }}
          >
            {ABOUT_GALLERY_IMAGES.map((image: GalleryImage) => (
              <div
                key={image.id}
                className="flex-shrink-0 w-96 h-96 overflow-hidden rounded-2xl shadow-eco-lg group cursor-pointer relative gallery-item border-3 border-brand-mint/30 hover:border-brand-teal/60 transition-all duration-500 hover:shadow-eco-xl"
                onClick={() => openLightbox(image.src)}
              >
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
                  loading="lazy"
                />
                {/* Eco-friendly overlay with gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-brand-teal/20 via-transparent to-brand-mint/10 opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-75 group-hover:scale-100">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-4 shadow-eco-lg border-2 border-brand-mint">
                      <svg className="w-8 h-8 text-brand-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                      </svg>
                    </div>
                  </div>
                </div>
                {/* Subtle eco-friendly corner accent with pulse */}
                <div className="absolute top-3 right-3 w-3 h-3 bg-brand-mint rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300 eco-pulse"></div>
              </div>
            ))}
          </div>

          {/* Enhanced Progress Bar */}
          <div className="flex justify-center mt-8">
            <div className="relative">
              <div className="w-80 h-2 bg-brand-mint/20 rounded-full overflow-hidden shadow-inner">
                <div
                  className="h-full bg-gradient-primary transition-all duration-500 ease-out rounded-full relative"
                  style={{ width: `${scrollProgress * 100}%` }}
                >
                  {/* Animated shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"></div>
                </div>
              </div>
              {/* Progress indicator dots */}
              <div className="absolute -top-1 left-0 w-full flex justify-between">
                {[...Array(5)].map((_, index) => (
                  <div
                    key={index}
                    className={`w-1 h-4 rounded-full transition-all duration-300 ${
                      scrollProgress >= index / 4
                        ? 'bg-brand-teal shadow-eco'
                        : 'bg-brand-mint/40'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Lightbox Modal */}
        {selectedImage && (
          <div
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4 lightbox-backdrop"
            onClick={closeLightbox}
          >
            <div className="relative max-w-5xl max-h-full">
              {/* Enhanced close button */}
              <button
                onClick={closeLightbox}
                className="absolute -top-16 right-0 bg-white/10 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/20 transition-all duration-300 shadow-lg hover:scale-110"
                aria-label="Close lightbox"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              {/* Image container with modern frame */}
              <div className="relative bg-white/5 backdrop-blur-md rounded-2xl p-2 shadow-2xl border border-white/10">
                <img
                  src={selectedImage}
                  alt="Gallery image"
                  className="max-w-full max-h-full object-contain rounded-xl shadow-lg"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default AboutGallery;
