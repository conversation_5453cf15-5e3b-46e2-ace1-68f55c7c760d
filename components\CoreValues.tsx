import React from 'react';
import { UsersGroupIcon } from './icons/UsersGroupIcon';
import { RocketLaunchIcon } from './icons/RocketLaunchIcon';
import { ChartBarIcon } from './icons/ChartBarIcon';

interface CoreValueItem {
  icon: React.ElementType;
  title: string;
  description: string;
}

const CORE_VALUES_DATA: CoreValueItem[] = [
  {
    icon: UsersGroupIcon,
    title: 'Great team work',
    description: 'Collaboration and mutual respect are the cornerstones of our success, driving innovation and exceptional service for every traveler.',
  },
  {
    icon: RocketLaunchIcon,
    title: 'Our vision',
    description: 'To be the leading innovator in travel, consistently inspiring global exploration and creating unforgettable, life-enriching journeys for everyone.',
  },
  {
    icon: ChartBarIcon,
    title: 'Our mission',
    description: 'To provide seamless, personalized, and enriching travel experiences through unwavering dedication, deep expertise, and a profound passion for discovery.',
  },
];

const CoreValueCard: React.FC<{ item: CoreValueItem }> = ({ item }) => {
  const Icon = item.icon;
  return (
    <div className="flex flex-col items-center text-center p-6 bg-white rounded-lg ">
      <div className="p-3 bg-brand-orange text-white rounded-full mb-4 inline-block">
        <Icon className="w-10 h-10" />
      </div>
      <h3 className="text-2xl font-semibold text-brand-blue-dark mb-2 font-poppins">{item.title}</h3>
      <p className="text-brand-gray text-base font-montserrat leading-relaxed">{item.description}</p>
    </div>
  );
};

const CoreValues: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 bg-brand-gray-light">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white p-8 md:p-12 rounded-xl shadow-xl border-2 border-brand-blue-light">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-10">
            {CORE_VALUES_DATA.map((value) => (
              <CoreValueCard key={value.title} item={value} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CoreValues;
