
import React, { useState, useEffect } from 'react';
import { NAV_LINKS } from '../constants';
import type { NavLink } from '../types';

interface NavbarProps {
  onNavigate: (keyOrId: string, isPage?: boolean) => void;
  currentPage: string;
}

const Navbar: React.FC<NavbarProps> = ({ onNavigate, currentPage }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect for navbar background
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLinkClick = (link: NavLink) => {
    if (typeof onNavigate === 'function') {
      onNavigate(link.href, link.isPage);
    } else {
      console.error(
        'Navbar Error: onNavigate prop is not a function. Received:',
        onNavigate,
        'Type:', typeof onNavigate
      );
    }
    setIsMobileMenuOpen(false); // Close mobile menu on navigation
  };

  const isLinkActive = (link: NavLink) => {
    if (link.isPage) {
      return link.href === currentPage;
    }
    // Basic active state for anchor links if on home page and hash matches
    if (currentPage === 'home' && typeof window !== 'undefined' && window.location.hash === link.href) {
      return true;
    }
    return false;
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 py-4 lg:py-6 font-poppins transition-all duration-500 ease-out ${
      isScrolled || currentPage !== 'home'
        ? 'bg-gradient-to-r from-forest-canopy/95 via-forest-trunk/95 to-forest-canopy/95 backdrop-blur-md shadow-eco-xl'
        : 'bg-transparent backdrop-blur-none'
    }`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center">
        <a
          href="#home"
          onClick={(e) => {
            e.preventDefault();
            // Ensure the object passed to handleLinkClick matches NavLink structure
            handleLinkClick({ href: 'home', label: 'Home', isPage: true });
          }}
          className={`text-3xl lg:text-4xl font-bold cursor-pointer transition-all duration-500 ${
            isScrolled || currentPage !== 'home'
              ? 'text-white drop-shadow-lg'
              : 'text-white drop-shadow-2xl'
          }`}
          aria-label="Go to homepage"
        >
          Rebel <span className={`transition-all duration-500 ${
            isScrolled || currentPage !== 'home'
              ? 'text-forest-light'
              : 'text-sunrise-gold drop-shadow-2xl'
          }`}>Rover</span>
        </a>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex space-x-8 items-center">
          {NAV_LINKS.map((link: NavLink) => (
            <a
              key={link.label}
              href={link.isPage ? `javascript:void(0)` : link.href}
              onClick={(e) => {
                if (link.isPage || link.href.startsWith('#')) e.preventDefault();
                handleLinkClick(link);
              }}
              className={`transition-all duration-500 text-lg font-medium relative group ${
                isScrolled || currentPage !== 'home'
                  ? 'text-white hover:text-forest-light drop-shadow-md'
                  : 'text-white hover:text-sunrise-gold drop-shadow-2xl'
              } ${
                isLinkActive(link)
                  ? isScrolled || currentPage !== 'home'
                    ? 'text-forest-light font-bold'
                    : 'text-sunrise-gold font-bold'
                  : ''
              }`}
              aria-current={isLinkActive(link) ? 'page' : undefined}
            >
              {link.label}
              {/* Animated underline */}
              <span className={`absolute bottom-0 left-0 w-0 h-0.5 transition-all duration-300 group-hover:w-full ${
                isScrolled || currentPage !== 'home'
                  ? 'bg-forest-light'
                  : 'bg-sunrise-gold'
              } ${isLinkActive(link) ? 'w-full' : ''}`}></span>
            </a>
          ))}
        </div>

        {/* Mobile Menu Button */}
        <div className="lg:hidden">
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className={`focus:outline-none p-2 rounded-lg transition-all duration-500 ${
              isScrolled || currentPage !== 'home'
                ? 'text-white hover:bg-forest-light/20 focus:ring-2 focus:ring-forest-light'
                : 'text-white hover:bg-sunrise-gold/20 focus:ring-2 focus:ring-sunrise-gold drop-shadow-2xl'
            }`}
            aria-label="Toggle mobile menu"
            aria-expanded={isMobileMenuOpen}
            aria-controls="mobile-menu"
          >
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                  aria-hidden="true"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16m-7 6h7"
                  aria-hidden="true"
                />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div
          id="mobile-menu"
          className={`lg:hidden absolute top-full left-0 right-0 mt-1 transition-all duration-500 ${
            isScrolled || currentPage !== 'home'
              ? 'bg-gradient-to-b from-forest-canopy/95 to-forest-deep/95 backdrop-blur-md shadow-eco-xl'
              : 'bg-gradient-to-b from-forest-deep/80 to-forest-canopy/80 backdrop-blur-lg shadow-2xl'
          }`}
        >
          <div className="container mx-auto px-4 sm:px-6 py-6 flex flex-col space-y-4 items-center">
            {NAV_LINKS.map((link: NavLink) => (
              <a
                key={link.label}
                href={link.isPage ? `javascript:void(0)` : link.href}
                onClick={(e) => {
                  if (link.isPage || link.href.startsWith('#')) e.preventDefault();
                  handleLinkClick(link);
                }}
                className={`transition-all duration-500 text-lg py-2 px-4 rounded-lg relative group ${
                  isScrolled || currentPage !== 'home'
                    ? 'text-white hover:text-forest-light hover:bg-forest-light/10'
                    : 'text-white hover:text-sunrise-gold hover:bg-sunrise-gold/10 drop-shadow-lg'
                } ${
                  isLinkActive(link)
                    ? isScrolled || currentPage !== 'home'
                      ? 'text-forest-light font-bold bg-forest-light/10'
                      : 'text-sunrise-gold font-bold bg-sunrise-gold/10'
                    : ''
                }`}
                aria-current={isLinkActive(link) ? 'page' : undefined}
              >
                {link.label}
              </a>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
