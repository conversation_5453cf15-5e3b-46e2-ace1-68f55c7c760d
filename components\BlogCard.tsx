import React from 'react';
import type { BlogPost } from '../types';

interface BlogCardProps {
  post: BlogPost;
  onReadMore?: (postId: string) => void;
  featured?: boolean;
}

const BlogCard: React.FC<BlogCardProps> = ({ post, onReadMore, featured = false }) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleReadMore = () => {
    if (onReadMore) {
      onReadMore(post.id);
    } else {
      // Fallback behavior - could scroll to top or show alert
      console.log(`Reading post: ${post.title}`);
    }
  };

  return (
    <article
      className={`eco-card rounded-2xl shadow-eco-lg overflow-hidden group transition-all duration-500 flex flex-col h-full relative ${
        featured ? 'ring-2 ring-brand-teal ring-opacity-60' : ''
      }`}
    >
      {/* Eco-friendly background accents */}
      <div className="absolute top-0 right-0 w-16 h-16 bg-brand-mint/10 rounded-full blur-lg"></div>
      <div className="absolute bottom-0 left-0 w-12 h-12 bg-brand-green/10 rounded-full blur-md"></div>

      {/* Featured Badge */}
      {featured && (
        <div className="absolute top-3 left-3 z-10 bg-gradient-primary text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-eco">
          🌟 Featured
        </div>
      )}

      {/* Image */}
      <div className="relative overflow-hidden h-48 rounded-t-2xl">
        <img
          src={post.image}
          alt={post.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110 group-hover:brightness-110"
        />
        {/* Eco overlay on image */}
        <div className="absolute inset-0 bg-gradient-to-t from-brand-teal/20 via-transparent to-brand-mint/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Eco corner accent */}
        <div className="absolute top-3 right-3 w-2 h-2 bg-brand-mint rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      {/* Content */}
      <div className="p-6 flex flex-col flex-grow relative z-10">
        {/* Category and Read Time */}
        <div className="flex items-center justify-between mb-3">
          <span className="inline-block bg-brand-mint/20 text-brand-teal text-xs font-medium px-3 py-1.5 rounded-full border border-brand-mint/30">
            {post.category}
          </span>
          <span className="text-brand-gray-dark text-xs bg-brand-cream/50 px-2 py-1 rounded-full">
            {post.readTime} min read
          </span>
        </div>

        {/* Title */}
        <h3 className="text-lg font-bold text-eco-primary mb-3 font-poppins line-clamp-2 group-hover:text-brand-teal transition-colors duration-300">
          {post.title}
        </h3>

        {/* Excerpt */}
        <p className="text-brand-gray-dark text-sm mb-4 line-clamp-2 flex-grow leading-relaxed">
          {post.excerpt}
        </p>

        {/* Author and Date */}
        <div className="flex items-center justify-between mt-auto">
          <div className="flex items-center">
            <img
              src={post.author.avatar}
              alt={post.author.name}
              className="w-8 h-8 rounded-full mr-3 border-2 border-brand-mint/30"
            />
            <div>
              <p className="text-xs font-medium text-brand-teal">{post.author.name}</p>
              <p className="text-xs text-brand-gray-dark">{formatDate(post.publishedDate)}</p>
            </div>
          </div>
          <button
            onClick={handleReadMore}
            className="eco-btn-primary px-4 py-2 rounded-lg text-xs font-medium transition-all duration-300"
            aria-label={`Read more about ${post.title}`}
          >
            🌿 Read More
          </button>
        </div>
      </div>
    </article>
  );
};

export default BlogCard;
