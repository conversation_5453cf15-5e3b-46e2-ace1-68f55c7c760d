import React from 'react';
import { CLIENT_LOGOS_DATA } from '../constants';
import type { ClientLogo } from '../types';

const ClientLogos: React.FC = () => {
  return (
    <section className="py-12 lg:py-16 eco-section-light relative overflow-hidden">
      {/* Subtle eco-friendly background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-24 h-24 bg-brand-teal rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-28 h-28 bg-brand-mint rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-20 h-20 bg-brand-green rounded-full blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-10 lg:mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-eco-primary mb-3 font-poppins">
            Trusted by Eco-Conscious Partners
          </h2>
          <p className="text-md text-brand-gray-dark max-w-xl mx-auto font-montserrat leading-relaxed">
            We are proud to partner with leading sustainable companies and eco-friendly organizations worldwide.
          </p>
        </div>
        <div className="flex flex-wrap justify-center items-center gap-x-8 gap-y-6 md:gap-x-12 lg:gap-x-16">
          {CLIENT_LOGOS_DATA.map((logo: ClientLogo) => (
            <div key={logo.id} className="h-12 flex items-center group">
              <img
                src={logo.src}
                alt={logo.alt}
                className="max-h-full max-w-[120px] object-contain opacity-60 group-hover:opacity-100 transition-all duration-300 filter grayscale group-hover:grayscale-0"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ClientLogos;
