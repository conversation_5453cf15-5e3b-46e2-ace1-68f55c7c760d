import React from 'react';
import { QuoteIcon } from './icons/QuoteIcon'; // Reusing existing quote icon

const FounderQuote: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center lg:gap-12">
          <div className="lg:w-1/3 mb-8 lg:mb-0">
            <img 
              src="https://picsum.photos/seed/founder/450/550" 
              alt="Siti <PERSON>, Founder of Rebel Rover" 
              className="rounded-xl shadow-2xl w-full max-w-md mx-auto h-auto object-cover aspect-[4/5]" 
            />
          </div>
          <div className="lg:w-2/3 text-center lg:text-left">
            <QuoteIcon className="w-12 h-12 text-brand-orange mb-6 mx-auto lg:mx-0" />
            <p className="text-xl md:text-2xl font-montserrat text-brand-gray italic leading-relaxed mb-8">
              "Our journey began with a simple idea: to make travel more accessible, meaningful, and personal. We believe in the transformative power of exploring new horizons, and we're here to guide you every step of the way."
            </p>
            <h3 className="text-2xl font-bold text-brand-blue-dark font-poppins">Siti Sarah</h3>
            <p className="text-md text-brand-orange font-semibold font-montserrat">Founder Rebel Rover</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FounderQuote;
