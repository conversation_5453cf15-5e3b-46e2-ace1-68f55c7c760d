import React, { useState, useMemo } from 'react';
import BlogH<PERSON> from '../components/BlogHero';
import BlogCard from '../components/BlogCard';
import Subscribe from '../components/Subscribe';
import { BLOG_POSTS_DATA } from '../constants';
import type { BlogPost } from '../types';

interface BlogPageProps {
  onReadPost?: (postId: string) => void;
}

const BlogPage: React.FC<BlogPageProps> = ({ onReadPost }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Get unique categories
  const categories = useMemo(() => {
    const cats = ['All', ...new Set(BLOG_POSTS_DATA.map(post => post.category))];
    return cats;
  }, []);

  // Filter posts based on category and search term
  const filteredPosts = useMemo(() => {
    let filtered = BLOG_POSTS_DATA;

    if (selectedCategory !== 'All') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    return filtered;
  }, [selectedCategory, searchTerm]);

  // Get featured posts
  const featuredPosts = useMemo(() => {
    return BLOG_POSTS_DATA.filter(post => post.featured);
  }, []);

  const handleReadMore = (postId: string) => {
    if (onReadPost) {
      onReadPost(postId);
    } else {
      // Fallback behavior
      alert(`Reading post: ${postId}. Blog post detail page coming soon!`);
    }
  };



  return (
    <>
      <BlogHero />

      {/* Featured Posts Section */}
      {featuredPosts.length > 0 && (
        <section className="py-16 lg:py-20 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-brand-blue-dark mb-3 font-poppins">
                Featured Stories
              </h2>
              <p className="text-lg text-brand-gray max-w-2xl mx-auto font-montserrat">
                Don't miss these handpicked stories from our latest adventures and expert insights.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {featuredPosts.map((post) => (
                <BlogCard
                  key={post.id}
                  post={post}
                  onReadMore={handleReadMore}
                  featured={true}
                />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Simple Blog Content */}
      <section className="py-16 lg:py-20 bg-brand-cream">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Simple Search and Filter */}
          <div className="max-w-4xl mx-auto mb-12">
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
              {/* Search Bar */}
              <div className="relative w-full sm:w-80">
                <input
                  type="text"
                  placeholder="Search stories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-3 pl-12 rounded-lg border border-brand-gray-medium focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent shadow-sm bg-white"
                />
                <svg
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-brand-gray"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>

              {/* Category Filter */}
              <div className="flex flex-wrap gap-2 justify-center">
                {categories.slice(0, 4).map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 shadow-sm ${
                      selectedCategory === category
                        ? 'bg-brand-teal text-white'
                        : 'bg-white text-brand-gray hover:bg-brand-teal hover:text-white'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>

            {/* Results Count */}
            {(searchTerm || selectedCategory !== 'All') && (
              <div className="text-center mt-6">
                <p className="text-brand-gray text-sm">
                  {filteredPosts.length === 0
                    ? 'No stories found. Try different search terms.'
                    : `Found ${filteredPosts.length} ${filteredPosts.length === 1 ? 'story' : 'stories'}`
                  }
                </p>
              </div>
            )}
          </div>

          {/* Blog Posts Grid */}
          {filteredPosts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
              {filteredPosts.map((post) => (
                <BlogCard
                  key={post.id}
                  post={post}
                  onReadMore={handleReadMore}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16 max-w-md mx-auto">
              <div className="w-20 h-20 mx-auto mb-6 bg-white rounded-full flex items-center justify-center shadow-lg">
                <svg className="w-10 h-10 text-brand-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-brand-blue-dark mb-2">No Stories Found</h3>
              <p className="text-brand-gray mb-6 text-sm">Try different search terms or browse all categories.</p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('All');
                }}
                className="bg-brand-teal text-white px-6 py-2 rounded-lg font-medium hover:bg-brand-teal-dark transition-colors duration-300"
              >
                Show All Stories
              </button>
            </div>
          )}
        </div>
      </section>

      <Subscribe />
    </>
  );
};

export default BlogPage;
