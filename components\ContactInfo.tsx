import React from 'react';
import { LocationMarkerIconOutline } from './icons/HeroIcons'; // Reusing existing icon
import { PhoneIcon, EnvelopeIcon } from './icons/ContactIcons';
import { ClockIcon } from './icons/ServiceIcons'; // Reusing existing icon

interface ContactDetailProps {
  icon: React.ElementType;
  label: string;
  value: string | React.ReactNode;
  href?: string;
}

const ContactDetailItem: React.FC<ContactDetailProps> = ({ icon: Icon, label, value, href }) => (
  <div className="flex items-start space-x-4">
    <div className="flex-shrink-0 w-10 h-10 bg-brand-orange text-white rounded-full flex items-center justify-center">
      <Icon className="w-5 h-5" />
    </div>
    <div>
      <h4 className="text-lg font-semibold text-brand-blue-dark font-poppins">{label}</h4>
      {href ? (
        <a href={href} className="text-brand-gray hover:text-brand-orange transition-colors break-words">
          {value}
        </a>
      ) : (
        <p className="text-brand-gray break-words">{value}</p>
      )}
    </div>
  </div>
);

const ContactInfo: React.FC = () => {
  const contactDetails: ContactDetailProps[] = [
    {
      icon: LocationMarkerIconOutline,
      label: 'Our Address',
      value: '123 Travel Lane, Wanderlust City, TX 78701, USA',
    },
    {
      icon: PhoneIcon,
      label: 'Phone Number',
      value: '(*************',
      href: 'tel:+15551234567',
    },
    {
      icon: EnvelopeIcon,
      label: 'Email Address',
      value: '<EMAIL>',
      href: 'mailto:<EMAIL>',
    },
    {
      icon: ClockIcon,
      label: 'Working Hours',
      value: (
        <>
          Monday - Friday: 9:00 AM - 6:00 PM <br />
          Saturday: 10:00 AM - 4:00 PM <br />
          Sunday: Closed
        </>
      ),
    },
  ];

  return (
    <div className="bg-white p-8 rounded-xl shadow-xl h-full">
      <h2 className="text-2xl md:text-3xl font-bold text-brand-blue-dark mb-8 font-poppins">Contact Information</h2>
      <div className="space-y-6">
        {contactDetails.map((detail) => (
          <ContactDetailItem key={detail.label} {...detail} />
        ))}
      </div>
    </div>
  );
};

export default ContactInfo;