
import React, { useEffect, useState } from 'react';

// Beautiful Sri Lankan tourist destinations for the slideshow
const HERO_IMAGES = [
  {
    id: 1,
    url: '/images/yves-alarie-3R50kTNBKiE-unsplash.jpg',
    alt: 'Beautiful blue train journey through scenic Sri Lankan landscapes'
  },
  {
    id: 2,
    url: '/images/anistanwatt-MPxBux4g5LU-unsplash.jpg',
    alt: 'Stunning Sri Lankan coastal scenery with pristine beaches'
  },
  {
    id: 3,
    url: '/images/sach-kKeC_lgVs_o-unsplash.jpg',
    alt: 'Majestic Sri Lankan mountain landscapes and tea plantations'
  },
  {
    id: 4,
    url: '/images/1000_F_67040696_h8lz94KbJuRmWP1vWBOJxTIXMRvWn38L.jpg',
    alt: 'Tropical paradise with palm trees and turquoise waters'
  }
];

const Hero: React.FC = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    // Preload all images for smooth transitions
    const preloadImages = () => {
      HERO_IMAGES.forEach((image) => {
        const img = new Image();
        img.src = image.url;
      });
    };

    preloadImages();

    // Auto-slide images every 6 seconds
    const slideInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === HERO_IMAGES.length - 1 ? 0 : prevIndex + 1
      );
    }, 6000);

    return () => clearInterval(slideInterval);
  }, []);

  return (
    <section id="home" className="relative min-h-screen flex flex-col justify-center items-center text-white pt-32 pb-12 md:pt-40 md:pb-20 overflow-hidden">
      {/* Image Slideshow Background */}
      <div className="absolute inset-0 z-0">
        {HERO_IMAGES.map((image, index) => (
          <div
            key={image.id}
            className={`absolute inset-0 hero-slide-transition ${
              index === currentImageIndex ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image.url}
              alt={image.alt}
              className={`w-full h-full object-cover ${
                index === currentImageIndex ? 'ken-burns-effect' : ''
              }`}
              loading={index === 0 ? 'eager' : 'lazy'}
            />
          </div>
        ))}
      </div>

      {/* Minimal overlay for text readability */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/10 via-black/20 to-black/30 z-1"></div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
        {HERO_IMAGES.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentImageIndex(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentImageIndex
                ? 'bg-white scale-125 slide-indicator-active'
                : 'bg-white/50 hover:bg-white/75'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Enhanced Content with Better Readability */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-poppins mb-6 md:mb-8 leading-tight text-white hero-text-shadow animate-fade-in-up">
          DISCOVER SRI LANKA <span className="text-emerald-400">LIKE NEVER BEFORE</span> WITH <span className="text-yellow-600">SHAN TRAVELS</span>
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl font-montserrat mb-10 md:mb-14 max-w-4xl mx-auto text-white leading-relaxed hero-text-shadow animate-fade-in-up animation-delay-300">
          From the golden beaches of Mirissa to the misty hills of Ella, we craft unforgettable travel experiences across Sri Lanka. Whether you're seeking adventure, culture, or pure relaxation, let us guide you to the island's hidden gems and iconic wonders.
        </p>
        <button
          onClick={() => {
            const packagesSection = document.getElementById('packages');
            if (packagesSection) {
              packagesSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          }}
          className="group relative px-16 py-6 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white text-xl font-bold rounded-full shadow-2xl hover:shadow-orange-500/50 transition-all duration-500 transform hover:scale-110 hover:-translate-y-2 animate-fade-in-up animation-delay-600 border-2 border-white/20 hover:border-white/40 backdrop-blur-sm overflow-hidden"
        >
          <span className="relative z-10 flex items-center gap-3">
            <svg className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Let's Explore
            <svg className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </span>
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out rounded-full"></div>
        </button>
      </div>
    </section>
  );
};

export default Hero;
