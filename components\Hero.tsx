
import React, { useEffect, useState } from 'react';

// Beautiful Sri Lankan tourist destinations for the slideshow
const HERO_IMAGES = [
  {
    id: 1,
    url: 'https://images.unsplash.com/photo-1566552881560-0be862a7c445?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: 'Sigiriya Rock Fortress - Ancient palace ruins atop dramatic rock formation in Sri Lanka'
  },
  {
    id: 2,
    url: 'https://unsplash.com/photos/blue-train-on-focus-photography-3R50kTNBKiE',
    alt: 'Nine Arch Bridge Ella - Iconic railway bridge surrounded by lush tea plantations'
  },
  {
    id: 3,
    url: 'https://images.unsplash.com/photo-1605640840605-14ac1855827b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: 'Nuwara Eliya Tea Plantations - Rolling green hills covered in tea bushes with misty mountains'
  },
  {
    id: 4,
    url: 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: 'Mirissa Beach - Pristine golden sand beach with palm trees and turquoise waters'
  },
  {
    id: 5,
    url: 'https://images.unsplash.com/photo-1544735716-392fe2489ffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: 'Kandy Temple of the Tooth - Sacred Buddhist temple with golden roofs and serene lake'
  },
  {
    id: 6,
    url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: 'Galle Fort - Historic Dutch colonial fort with lighthouse overlooking the Indian Ocean'
  }
];

const Hero: React.FC = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    // Preload all images for smooth transitions
    const preloadImages = () => {
      HERO_IMAGES.forEach((image) => {
        const img = new Image();
        img.src = image.url;
      });
    };

    preloadImages();

    // Auto-slide images every 6 seconds
    const slideInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === HERO_IMAGES.length - 1 ? 0 : prevIndex + 1
      );
    }, 6000);

    return () => clearInterval(slideInterval);
  }, []);

  return (
    <section id="home" className="relative min-h-screen flex flex-col justify-center items-center text-white pt-32 pb-12 md:pt-40 md:pb-20 overflow-hidden">
      {/* Image Slideshow Background */}
      <div className="absolute inset-0 z-0">
        {HERO_IMAGES.map((image, index) => (
          <div
            key={image.id}
            className={`absolute inset-0 hero-slide-transition ${
              index === currentImageIndex ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image.url}
              alt={image.alt}
              className={`w-full h-full object-cover ${
                index === currentImageIndex ? 'ken-burns-effect' : ''
              }`}
              loading={index === 0 ? 'eager' : 'lazy'}
            />
          </div>
        ))}
      </div>

      {/* Minimal overlay for text readability */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/10 via-black/20 to-black/30 z-1"></div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
        {HERO_IMAGES.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentImageIndex(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentImageIndex
                ? 'bg-white scale-125 slide-indicator-active'
                : 'bg-white/50 hover:bg-white/75'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Enhanced Content with Better Readability */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-poppins mb-4 md:mb-6 leading-tight text-white hero-text-shadow eco-fade-in-energy">
          EXPLORE NATURE WITH <span className="text-sunrise-gold eco-shimmer">REBEL ROVER</span>
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl font-montserrat mb-8 md:mb-12 max-w-3xl mx-auto text-white leading-relaxed hero-text-shadow eco-fade-in-trust">
          Discover sustainable travel experiences that connect you with nature while preserving our planet for future generations through transformative eco-conscious adventures.
        </p>
        <button className="eco-btn-action px-12 py-5 rounded-xl text-lg font-semibold transition-all duration-300 transform shadow-eco-lg eco-float">
          🌿 Explore Eco-Adventures
        </button>
      </div>
    </section>
  );
};

export default Hero;
