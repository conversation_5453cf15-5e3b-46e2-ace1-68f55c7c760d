//FIX: Added FeatureInfo to the import statement
import type { NavLink, PackageDeal, Destination, Testimonial, ServiceInfo, FeatureInfo, GalleryImage, ClientLogo, BlogPost } from './types';
import { BriefcaseIcon, UsersIcon as FeatureUsersIcon, SparklesIcon, ShieldCheckIcon } from './components/icons/FeatureIcons'; // Renamed UsersIcon to avoid conflict
import { PlaneIcon, BuildingIcon, CogIcon, BedIcon, GlobeIcon, ClockIcon } from './components/icons/ServiceIcons';


export const NAV_LINKS: NavLink[] = [
  { href: 'home', label: 'Home', isPage: true },
  { href: 'about', label: 'About Us', isPage: true },
  { href: 'packages', label: 'Packages', isPage: true },
  { href: 'services', label: 'Services', isPage: true },
  { href: 'blog', label: 'Blog', isPage: true },
  { href: 'contact', label: 'Contact', isPage: true }, // Updated to be a page link
];

export const WHY_CHOOSE_US_FEATURES: FeatureInfo[] = [
  {
    icon: ShieldCheckIcon,
    title: 'Best Price Guarantee',
    description: 'Travel with confidence knowing you are getting the best price for your adventure.',
  },
  {
    icon: FeatureUsersIcon, // This is the group icon
    title: '24/7 Customer Support',
    description: 'Our dedicated team is always here to help you, any time of day or night.',
  },
  {
    icon: BriefcaseIcon,
    title: 'Expert Travel Guides',
    description: 'Explore with knowledgeable guides who bring destinations to life.',
  },
  {
    icon: SparklesIcon,
    title: 'Customizable Packages',
    description: 'Tailor your trip to your preferences for a truly personal experience.',
  },
];

export const OUR_SERVICES_DATA: ServiceInfo[] = [
  {
    icon: PlaneIcon,
    title: 'Airport Pickups & Drops',
    description: 'Hassle-free airport transfers for a smooth start and end to your journey, ensuring timely and comfortable rides.',
    image: 'https://picsum.photos/seed/airportservice/600/400',
  },
  {
    icon: BuildingIcon,
    title: 'City Tours',
    description: 'Explore iconic landmarks and hidden gems with our expertly guided city tours, customized to your interests.',
    image: 'https://picsum.photos/seed/citytour/600/400',
  },
  {
    icon: CogIcon,
    title: 'Customized Travel Plans',
    description: 'Tailor-made itineraries designed to match your unique interests, budget, and travel style for a personal adventure.',
    image: 'https://picsum.photos/seed/customplan/600/400',
  },
  {
    icon: BedIcon,
    title: 'Hotel Bookings',
    description: 'Access a wide range of comfortable and convenient accommodation options, booked at the best competitive rates.',
    image: 'https://picsum.photos/seed/hotelbooking/600/400',
  },
  {
    icon: GlobeIcon,
    title: 'Total Travel Solutions',
    description: 'Comprehensive, end-to-end travel planning, from flights and hotels to activities, visas, and continuous support.',
    image: 'https://picsum.photos/seed/travelsolution/600/400',
  },
  {
    icon: ClockIcon,
    title: 'Flexible Timings',
    description: 'Travel on your own schedule with our adaptable tour and service timings, offering you maximum convenience.',
    image: 'https://picsum.photos/seed/flexibletime/600/400',
  },
];


export const OUR_PACKAGES_DATA: PackageDeal[] = [
  {
    id: 'sl-cultural-1',
    image: 'https://picsum.photos/seed/sigiriya/400/300',
    location: 'Sri Lanka',
    title: 'Cultural Triangle Immersion',
    price: 850,
    rating: 4.7,
    duration: '7 Days 6 Nights',
    shortDescription: 'Discover ancient kingdoms and sacred sites in Sri Lanka\'s historic heartland.',
    detailedDescription: 'Embark on a captivating journey through Sri Lanka\'s Cultural Triangle. Explore the majestic Sigiriya Rock Fortress, the ancient city of Polonnaruwa, and the sacred Dambulla Cave Temple. This tour offers a deep dive into the island\'s rich history and vibrant culture, with comfortable accommodations and expert local guides.',
    itineraryHighlights: [
      'Ascend Sigiriya Rock Fortress for panoramic views.',
      'Explore the ancient ruins of Polonnaruwa by bicycle.',
      'Visit the Dambulla Cave Temple, a UNESCO World Heritage site.',
      'Experience a traditional Sri Lankan cooking class.',
      'Optional visit to a local village for cultural interaction.'
    ],
    inclusions: ['Accommodation in 3-4 star hotels', 'Daily breakfast', 'Private air-conditioned transport', 'English-speaking chauffeur guide', 'Entrance fees to Sigiriya, Polonnaruwa, Dambulla', 'Cooking class'],
    exclusions: ['International flights', 'Lunch and Dinner', 'Visa fees', 'Personal expenses', 'Tips for guide/driver'],
    galleryImages: [
        'https://picsum.photos/seed/sigiriya-gallery1/800/600',
        'https://picsum.photos/seed/polonnaruwa-gallery2/800/600',
        'https://picsum.photos/seed/dambulla-gallery3/800/600',
        'https://picsum.photos/seed/sl-culture-gallery4/800/600',
    ],
    mapEmbedUrl: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1010308.1682381747!2d79.87937036595113!3d7.845597718285461!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3afb1d5e3f670f45%3A0x1f78b3844db5a4a5!2sCultural%20Triangle!5e0!3m2!1sen!2slk!4v1628588982085!5m2!1sen!2slk'
  },
  {
    id: 'sl-hills-2',
    image: 'https://picsum.photos/seed/ella/400/300',
    location: 'Sri Lanka',
    title: 'Scenic Hill Country & Tea Trails',
    price: 920,
    rating: 4.8,
    duration: '6 Days 5 Nights',
    shortDescription: 'Journey through misty mountains, lush tea plantations, and charming colonial towns.',
    detailedDescription: 'Experience the breathtaking beauty of Sri Lanka\'s hill country. Ride the famous scenic train to Ella, walk through verdant tea estates in Nuwara Eliya (Little England), and discover the cultural richness of Kandy, home to the Temple of the Sacred Tooth Relic. This tour combines stunning landscapes with unique cultural encounters.',
    itineraryHighlights: [
      'Visit the Temple of the Sacred Tooth Relic in Kandy.',
      'Explore a tea plantation and factory in Nuwara Eliya.',
      'Scenic train journey from Kandy/Nuwara Eliya to Ella.',
      'Hike to Little Adam\'s Peak or Ella Rock for stunning views.',
      'Walk across the Nine Arch Bridge in Ella.'
    ],
    inclusions: ['Accommodation in boutique hotels/guesthouses', 'Daily breakfast', 'Private air-conditioned transport', 'English-speaking chauffeur guide', 'Train tickets (observation class subject to availability)', 'Entrance fees to Temple of Tooth, Tea Factory'],
    exclusions: ['International flights', 'Lunch and Dinner', 'Visa fees', 'Personal expenses', 'Tips'],
    galleryImages: [
        'https://picsum.photos/seed/kandy-gallery1/800/600',
        'https://picsum.photos/seed/nuwaraeliya-gallery2/800/600',
        'https://picsum.photos/seed/ella-train-gallery3/800/600',
        'https://picsum.photos/seed/ninearch-gallery4/800/600',
    ]
  },
  {
    id: 'sl-beaches-3',
    image: 'https://picsum.photos/seed/galle/400/300',
    location: 'Sri Lanka',
    title: 'Southern Coast & Galle Fort Adventure',
    price: 780,
    rating: 4.6,
    duration: '5 Days 4 Nights',
    shortDescription: 'Relax on golden beaches, explore colonial history, and spot stilt fishermen.',
    detailedDescription: 'Discover the charm of Sri Lanka\'s southern coast. Wander through the historic Galle Fort, a UNESCO World Heritage site, relax on the beautiful beaches of Unawatuna or Mirissa, and perhaps even spot some whales (seasonal). This tour is a perfect blend of relaxation, culture, and coastal beauty.',
    itineraryHighlights: [
      'Guided tour of Galle Fort, exploring its ramparts and colonial architecture.',
      'Relax on the beaches of Unawatuna or Mirissa.',
      'Optional whale watching tour from Mirissa (seasonal).',
      'Visit a turtle hatchery.',
      'See the iconic stilt fishermen (seasonal).'
    ],
    inclusions: ['Beachfront hotel accommodation', 'Daily breakfast', 'Private air-conditioned transport', 'English-speaking chauffeur guide', 'Entrance to Galle Fort sites', 'Turtle hatchery visit'],
    exclusions: ['International flights', 'Lunch and Dinner', 'Whale watching fees', 'Visa fees', 'Personal expenses'],
    galleryImages: [
        'https://picsum.photos/seed/gallefort-gallery1/800/600',
        'https://picsum.photos/seed/unawatuna-gallery2/800/600',
        'https://picsum.photos/seed/mirissa-gallery3/800/600',
        'https://picsum.photos/seed/stiltfishermen-gallery4/800/600',
    ]
  },
  {
    id: '1',
    image: 'https://picsum.photos/seed/switzerland/400/300',
    location: 'Switzerland',
    title: 'Beautiful Switzerland Tour',
    price: 1200,
    rating: 4.5,
    duration: '3 Days 2 Nights',
    shortDescription: 'Placeholder short description for Switzerland.',
    detailedDescription: 'Placeholder detailed description for Switzerland.',
    itineraryHighlights: ['Highlight 1', 'Highlight 2'],
    inclusions: ['Inclusion 1'],
    exclusions: ['Exclusion 1'],
    galleryImages: ['https://picsum.photos/seed/switzerland-gallery1/800/600'],
  },
  {
    id: '2',
    image: 'https://picsum.photos/seed/paris/400/300',
    location: 'Paris, France',
    title: 'Romantic Paris Getaway',
    price: 950,
    rating: 4.8,
    duration: '4 Days 3 Nights',
    shortDescription: 'Placeholder short description for Paris.',
    detailedDescription: 'Placeholder detailed description for Paris.',
    itineraryHighlights: ['Highlight 1', 'Highlight 2'],
    inclusions: ['Inclusion 1'],
    exclusions: ['Exclusion 1'],
    galleryImages: ['https://picsum.photos/seed/paris-gallery1/800/600'],
  },
  {
    id: '3',
    image: 'https://picsum.photos/seed/bali/400/300',
    location: 'Bali, Indonesia',
    title: 'Bali Adventure Trip',
    price: 700,
    rating: 4.2,
    duration: '5 Days 4 Nights',
    shortDescription: 'Placeholder short description for Bali.',
    detailedDescription: 'Placeholder detailed description for Bali.',
    itineraryHighlights: ['Highlight 1', 'Highlight 2'],
    inclusions: ['Inclusion 1'],
    exclusions: ['Exclusion 1'],
    galleryImages: ['https://picsum.photos/seed/bali-gallery1/800/600'],
  },
   {
    id: '4',
    image: 'https://picsum.photos/seed/rome/400/300',
    location: 'Rome, Italy',
    title: 'Ancient Rome Exploration',
    price: 1100,
    rating: 4.6,
    duration: '3 Days 2 Nights',
    shortDescription: 'Placeholder short description for Rome.',
    detailedDescription: 'Placeholder detailed description for Rome.',
    itineraryHighlights: ['Highlight 1', 'Highlight 2'],
    inclusions: ['Inclusion 1'],
    exclusions: ['Exclusion 1'],
    galleryImages: ['https://picsum.photos/seed/rome-gallery1/800/600'],
  },
  {
    id: '5',
    image: 'https://picsum.photos/seed/tokyo/400/300',
    location: 'Tokyo, Japan',
    title: 'Modern Tokyo Experience',
    price: 1500,
    rating: 4.9,
    duration: '5 Days 4 Nights',
    shortDescription: 'Placeholder short description for Tokyo.',
    detailedDescription: 'Placeholder detailed description for Tokyo.',
    itineraryHighlights: ['Highlight 1', 'Highlight 2'],
    inclusions: ['Inclusion 1'],
    exclusions: ['Exclusion 1'],
    galleryImages: ['https://picsum.photos/seed/tokyo-gallery1/800/600'],
  },
  {
    id: '6',
    image: 'https://picsum.photos/seed/newyork/400/300',
    location: 'New York, USA',
    title: 'NYC Big Apple Tour',
    price: 1350,
    rating: 4.7,
    duration: '4 Days 3 Nights',
    shortDescription: 'Placeholder short description for NYC.',
    detailedDescription: 'Placeholder detailed description for NYC.',
    itineraryHighlights: ['Highlight 1', 'Highlight 2'],
    inclusions: ['Inclusion 1'],
    exclusions: ['Exclusion 1'],
    galleryImages: ['https://picsum.photos/seed/newyork-gallery1/800/600'],
  },
  {
    id: '7',
    image: 'https://picsum.photos/seed/london/400/300',
    location: 'London, UK',
    title: 'Classic London Sights',
    price: 1150,
    rating: 4.4,
    duration: '3 Days 2 Nights',
    shortDescription: 'Placeholder short description for London.',
    detailedDescription: 'Placeholder detailed description for London.',
    itineraryHighlights: ['Highlight 1', 'Highlight 2'],
    inclusions: ['Inclusion 1'],
    exclusions: ['Exclusion 1'],
    galleryImages: ['https://picsum.photos/seed/london-gallery1/800/600'],
  },
  {
    id: '8',
    image: 'https://picsum.photos/seed/dubai/400/300',
    location: 'Dubai, UAE',
    title: 'Luxury Dubai Escape',
    price: 1800,
    rating: 4.9,
    duration: '6 Days 5 Nights',
    shortDescription: 'Placeholder short description for Dubai.',
    detailedDescription: 'Placeholder detailed description for Dubai.',
    itineraryHighlights: ['Highlight 1', 'Highlight 2'],
    inclusions: ['Inclusion 1'],
    exclusions: ['Exclusion 1'],
    galleryImages: ['https://picsum.photos/seed/dubai-gallery1/800/600'],
  },
];

export const FEATURED_DESTINATIONS_DATA: Destination[] = [
  { id: '1', name: 'Paris', image: 'https://picsum.photos/seed/dest-paris/600/800' },
  { id: '2', name: 'Rome', image: 'https://picsum.photos/seed/dest-rome/600/800' },
  { id: '3', name: 'Bali', image: 'https://picsum.photos/seed/dest-bali/600/800' },
  { id: '4', name: 'Tokyo', image: 'https://picsum.photos/seed/dest-tokyo/600/800' },
  { id: '5', name: 'Santorini', image: 'https://picsum.photos/seed/dest-santorini/600/800' },
  { id: '6', name: 'Maldives', image: 'https://picsum.photos/seed/dest-maldives/600/800' },
  { id: '7', name: 'Iceland', image: 'https://picsum.photos/seed/dest-iceland/600/800' },
  { id: '8', name: 'Dubai', image: 'https://picsum.photos/seed/dest-dubai/600/800' },
];

export const TESTIMONIALS_DATA: Testimonial[] = [
  {
    id: '1',
    avatar: 'https://picsum.photos/seed/avatar1/100/100',
    name: 'Sarah L.',
    review: 'Shan Travels made my dream vacation a reality! The entire process was seamless, and the trip itself was unforgettable. Highly recommend!',
    rating: 5,
  },
  {
    id: '2',
    avatar: 'https://picsum.photos/seed/avatar2/100/100',
    name: 'John B.',
    review: 'Amazing experience from start to finish. The customer support was fantastic, and our guide was incredibly knowledgeable. Will book again!',
    rating: 4.5,
  },
  {
    id: '3',
    avatar: 'https://picsum.photos/seed/avatar3/100/100',
    name: 'Emily K.',
    review: 'I was looking for a unique adventure, and Rebel Rover delivered. The customizable package option was perfect for me. Thank you!',
    rating: 5,
  },
];

export const ABOUT_GALLERY_IMAGES: GalleryImage[] = [
  { id: 'g1', src: 'https://picsum.photos/seed/aboutgallery1/600/600', alt: 'Scenic mountain landscape' },
  { id: 'g2', src: 'https://picsum.photos/seed/aboutgallery2/600/600', alt: 'Team outing photo' },
  { id: 'g3', src: 'https://picsum.photos/seed/aboutgallery3/600/600', alt: 'Happy customer on a tour' },
  { id: 'g4', src: 'https://picsum.photos/seed/aboutgallery4/600/600', alt: 'Company event picture' },
  { id: 'g5', src: 'https://picsum.photos/seed/aboutgallery5/600/600', alt: 'Beautiful sunset at destination' },
  { id: 'g6', src: 'https://picsum.photos/seed/aboutgallery6/600/600', alt: 'Adventure hiking trail' },
  { id: 'g7', src: 'https://picsum.photos/seed/aboutgallery7/600/600', alt: 'Cultural heritage site visit' },
  { id: 'g8', src: 'https://picsum.photos/seed/aboutgallery8/600/600', alt: 'Beach vacation memories' },
  { id: 'g9', src: 'https://picsum.photos/seed/aboutgallery9/600/600', alt: 'City exploration tour' },
  { id: 'g10', src: 'https://picsum.photos/seed/aboutgallery10/600/600', alt: 'Wildlife safari experience' },
  { id: 'g11', src: 'https://picsum.photos/seed/aboutgallery11/600/600', alt: 'Local cuisine tasting' },
  { id: 'g12', src: 'https://picsum.photos/seed/aboutgallery12/600/600', alt: 'Traditional festival celebration' },
  { id: 'g13', src: 'https://picsum.photos/seed/aboutgallery13/600/600', alt: 'Luxury accommodation experience' },
  { id: 'g14', src: 'https://picsum.photos/seed/aboutgallery14/600/600', alt: 'Adventure sports activity' },
  { id: 'g15', src: 'https://picsum.photos/seed/aboutgallery15/600/600', alt: 'Peaceful nature retreat' },
  { id: 'g16', src: 'https://picsum.photos/seed/aboutgallery16/600/600', alt: 'Group travel memories' },
];

export const CLIENT_LOGOS_DATA: ClientLogo[] = [
  { id: 'cl1', src: 'https://picsum.photos/seed/logo1/150/50?grayscale&random=1', alt: 'Client Logo 1' },
  { id: 'cl2', src: 'https://picsum.photos/seed/logo2/150/50?grayscale&random=2', alt: 'Client Logo 2' },
  { id: 'cl3', src: 'https://picsum.photos/seed/logo3/150/50?grayscale&random=3', alt: 'Client Logo 3' },
  { id: 'cl4', src: 'https://picsum.photos/seed/logo4/150/50?grayscale&random=4', alt: 'Client Logo 4' },
  { id: 'cl5', src: 'https://picsum.photos/seed/logo5/150/50?grayscale&random=5', alt: 'Client Logo 5' },
];

export const BLOG_POSTS_DATA: BlogPost[] = [
  {
    id: 'blog-1',
    title: 'Top 10 Hidden Gems in Southeast Asia',
    excerpt: 'Discover breathtaking destinations off the beaten path that will transform your travel experience and create unforgettable memories.',
    content: 'Southeast Asia is home to countless hidden treasures waiting to be explored. From pristine beaches to ancient temples, these destinations offer authentic experiences away from the crowds...',
    image: 'https://picsum.photos/seed/blog-sea/800/600',
    author: {
      name: 'Sarah Johnson',
      avatar: 'https://picsum.photos/seed/author-sarah/100/100'
    },
    category: 'Destinations',
    tags: ['Southeast Asia', 'Hidden Gems', 'Adventure', 'Culture'],
    publishedDate: '2024-01-15',
    readTime: 8,
    featured: true
  },
  {
    id: 'blog-2',
    title: 'Sustainable Travel: How to Explore Responsibly',
    excerpt: 'Learn practical tips for eco-friendly travel that minimizes your environmental impact while maximizing your cultural experiences.',
    content: 'Sustainable travel is more than just a trend—it\'s a responsibility. Here are actionable ways to reduce your carbon footprint while exploring the world...',
    image: 'https://picsum.photos/seed/blog-sustainable/800/600',
    author: {
      name: 'Michael Chen',
      avatar: 'https://picsum.photos/seed/author-michael/100/100'
    },
    category: 'Eco Travel',
    tags: ['Sustainability', 'Eco-friendly', 'Responsible Travel', 'Environment'],
    publishedDate: '2024-01-12',
    readTime: 6,
    featured: true
  },
  {
    id: 'blog-3',
    title: 'Budget Travel Hacks: See More, Spend Less',
    excerpt: 'Master the art of budget travel with insider tips that help you stretch your money further without compromising on experiences.',
    content: 'Traveling on a budget doesn\'t mean sacrificing quality. These proven strategies will help you save money while still having amazing adventures...',
    image: 'https://picsum.photos/seed/blog-budget/800/600',
    author: {
      name: 'Emma Rodriguez',
      avatar: 'https://picsum.photos/seed/author-emma/100/100'
    },
    category: 'Travel Tips',
    tags: ['Budget Travel', 'Money Saving', 'Travel Hacks', 'Backpacking'],
    publishedDate: '2024-01-10',
    readTime: 7
  },
  {
    id: 'blog-4',
    title: 'Cultural Immersion: Beyond Tourist Attractions',
    excerpt: 'Dive deep into local cultures and create meaningful connections that go beyond typical tourist experiences.',
    content: 'True cultural immersion requires stepping outside your comfort zone and engaging with local communities in authentic ways...',
    image: 'https://picsum.photos/seed/blog-culture/800/600',
    author: {
      name: 'David Kim',
      avatar: 'https://picsum.photos/seed/author-david/100/100'
    },
    category: 'Culture',
    tags: ['Cultural Travel', 'Local Experiences', 'Authentic Travel', 'Community'],
    publishedDate: '2024-01-08',
    readTime: 9
  },
  {
    id: 'blog-5',
    title: 'Solo Female Travel: Safety and Empowerment',
    excerpt: 'Essential safety tips and empowering advice for women traveling solo, from planning to execution.',
    content: 'Solo female travel can be incredibly rewarding and empowering. Here\'s everything you need to know to travel safely and confidently...',
    image: 'https://picsum.photos/seed/blog-solo-female/800/600',
    author: {
      name: 'Lisa Thompson',
      avatar: 'https://picsum.photos/seed/author-lisa/100/100'
    },
    category: 'Solo Travel',
    tags: ['Solo Travel', 'Female Travel', 'Safety', 'Empowerment'],
    publishedDate: '2024-01-05',
    readTime: 10
  },
  {
    id: 'blog-6',
    title: 'Adventure Photography: Capturing Epic Moments',
    excerpt: 'Professional tips for taking stunning travel photos that tell compelling stories and preserve your adventures forever.',
    content: 'Great travel photography is about more than just having the right equipment. Learn how to capture the essence of your adventures...',
    image: 'https://picsum.photos/seed/blog-photography/800/600',
    author: {
      name: 'Alex Martinez',
      avatar: 'https://picsum.photos/seed/author-alex/100/100'
    },
    category: 'Photography',
    tags: ['Photography', 'Adventure', 'Travel Tips', 'Visual Storytelling'],
    publishedDate: '2024-01-03',
    readTime: 12
  },
  {
    id: 'blog-7',
    title: 'Digital Nomad Essentials: Work While You Wander',
    excerpt: 'Complete guide to becoming a successful digital nomad, including tools, destinations, and lifestyle tips.',
    content: 'The digital nomad lifestyle offers unprecedented freedom, but it requires careful planning and the right tools to succeed...',
    image: 'https://picsum.photos/seed/blog-nomad/800/600',
    author: {
      name: 'Rachel Green',
      avatar: 'https://picsum.photos/seed/author-rachel/100/100'
    },
    category: 'Digital Nomad',
    tags: ['Digital Nomad', 'Remote Work', 'Lifestyle', 'Technology'],
    publishedDate: '2024-01-01',
    readTime: 11
  },
  {
    id: 'blog-8',
    title: 'Foodie Adventures: Culinary Journeys Around the World',
    excerpt: 'Explore the world through its flavors with our guide to the best culinary destinations and food experiences.',
    content: 'Food is one of the best ways to experience a culture. Join us on a culinary journey to discover amazing flavors around the globe...',
    image: 'https://picsum.photos/seed/blog-food/800/600',
    author: {
      name: 'James Wilson',
      avatar: 'https://picsum.photos/seed/author-james/100/100'
    },
    category: 'Food & Culture',
    tags: ['Food Travel', 'Culinary', 'Culture', 'Local Cuisine'],
    publishedDate: '2023-12-28',
    readTime: 8
  },
  {
    id: 'blog-9',
    title: 'Wellness Retreats: Rejuvenate Your Mind and Body',
    excerpt: 'Discover transformative wellness retreats that combine travel with personal growth and rejuvenation.',
    content: 'Wellness travel is about more than relaxation—it\'s about transformation. These retreats offer life-changing experiences...',
    image: 'https://picsum.photos/seed/blog-wellness/800/600',
    author: {
      name: 'Sophia Lee',
      avatar: 'https://picsum.photos/seed/author-sophia/100/100'
    },
    category: 'Wellness',
    tags: ['Wellness', 'Retreats', 'Mindfulness', 'Health'],
    publishedDate: '2023-12-25',
    readTime: 9
  },
  {
    id: 'blog-10',
    title: 'Sri Lanka\'s Ancient Wonders: Exploring the Cultural Triangle',
    excerpt: 'Journey through Sri Lanka\'s most sacred and historically significant sites, from the majestic Sigiriya Rock Fortress to the ancient ruins of Polonnaruwa.',
    content: 'Sri Lanka\'s Cultural Triangle is a treasure trove of ancient wonders that showcase the island\'s rich heritage. Discover the engineering marvels of Sigiriya, the spiritual significance of Anuradhapura, and the artistic brilliance of Dambulla Cave Temple...',
    image: 'https://picsum.photos/seed/blog-sigiriya/800/600',
    author: {
      name: 'Priya Perera',
      avatar: 'https://picsum.photos/seed/author-priya/100/100'
    },
    category: 'Sri Lanka',
    tags: ['Sri Lanka', 'Cultural Triangle', 'Sigiriya', 'Ancient Sites', 'Heritage'],
    publishedDate: '2024-01-20',
    readTime: 10,
    featured: true
  },
  {
    id: 'blog-11',
    title: 'Hill Country Magic: Tea Plantations and Cool Climate Escapes',
    excerpt: 'Escape to Sri Lanka\'s misty hill country where emerald tea plantations, colonial charm, and breathtaking mountain views create an unforgettable experience.',
    content: 'The central highlands of Sri Lanka offer a refreshing contrast to the tropical lowlands. From the tea estates of Nuwara Eliya to the scenic train journey to Ella, discover why the hill country is considered the crown jewel of Sri Lankan tourism...',
    image: 'https://picsum.photos/seed/blog-tea-plantation/800/600',
    author: {
      name: 'Chaminda Silva',
      avatar: 'https://picsum.photos/seed/author-chaminda/100/100'
    },
    category: 'Sri Lanka',
    tags: ['Sri Lanka', 'Hill Country', 'Tea Plantations', 'Nuwara Eliya', 'Ella', 'Train Journey'],
    publishedDate: '2024-01-18',
    readTime: 8
  },
  {
    id: 'blog-12',
    title: 'Coastal Paradise: Sri Lanka\'s Best Beaches and Marine Life',
    excerpt: 'From the golden beaches of the south coast to the pristine waters of the east, explore Sri Lanka\'s stunning coastline and incredible marine biodiversity.',
    content: 'Sri Lanka\'s coastline offers some of the world\'s most beautiful beaches and diverse marine ecosystems. Whether you\'re seeking surfing adventures in Arugam Bay, whale watching in Mirissa, or simply relaxing on the pristine sands of Unawatuna...',
    image: 'https://picsum.photos/seed/blog-sri-lanka-beach/800/600',
    author: {
      name: 'Nuwan Fernando',
      avatar: 'https://picsum.photos/seed/author-nuwan/100/100'
    },
    category: 'Sri Lanka',
    tags: ['Sri Lanka', 'Beaches', 'Marine Life', 'Whale Watching', 'Surfing', 'Coastal Tourism'],
    publishedDate: '2024-01-16',
    readTime: 9
  },
  {
    id: 'blog-13',
    title: 'Wildlife Safari Adventures: Leopards, Elephants, and More',
    excerpt: 'Embark on thrilling wildlife safaris in Sri Lanka\'s national parks, home to leopards, elephants, and an incredible array of endemic species.',
    content: 'Sri Lanka punches above its weight when it comes to wildlife diversity. From the leopard-rich Yala National Park to the elephant gatherings at Minneriya, discover the best wildlife experiences this island nation has to offer...',
    image: 'https://picsum.photos/seed/blog-sri-lanka-wildlife/800/600',
    author: {
      name: 'Tharanga Wickramasinghe',
      avatar: 'https://picsum.photos/seed/author-tharanga/100/100'
    },
    category: 'Sri Lanka',
    tags: ['Sri Lanka', 'Wildlife', 'Safari', 'National Parks', 'Leopards', 'Elephants'],
    publishedDate: '2024-01-14',
    readTime: 11
  },
  {
    id: 'blog-14',
    title: 'Culinary Journey Through Sri Lanka: Spices, Flavors, and Traditions',
    excerpt: 'Discover the rich culinary heritage of Sri Lanka, from fiery curries and aromatic spices to sweet treats and traditional cooking methods.',
    content: 'Sri Lankan cuisine is a symphony of flavors that reflects the island\'s diverse cultural influences. Explore the art of curry making, the significance of rice and curry, and the unique cooking traditions that make Sri Lankan food unforgettable...',
    image: 'https://picsum.photos/seed/blog-sri-lanka-food/800/600',
    author: {
      name: 'Malini Jayawardena',
      avatar: 'https://picsum.photos/seed/author-malini/100/100'
    },
    category: 'Sri Lanka',
    tags: ['Sri Lanka', 'Cuisine', 'Spices', 'Curry', 'Food Culture', 'Traditional Cooking'],
    publishedDate: '2024-01-12',
    readTime: 7
  }
];