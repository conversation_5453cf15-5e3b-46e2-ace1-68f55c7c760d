import React from 'react';
import { UsersIcon as FeatureUsersIcon } from './icons/FeatureIcons'; // For satisfied clients
import { UserPlusIcon } from './icons/UserPlusIcon';
import { MountainIcon } from './icons/MountainIcon';
import { AwardIcon } from './icons/AwardIcon';

interface StatItem {
  icon: React.ElementType;
  count: string;
  label: string;
}

const STATS_DATA: StatItem[] = [
  { icon: FeatureUsersIcon, count: '126+', label: 'Satisfied Client' },
  { icon: UserPlusIcon, count: '230+', label: 'New Traveller' },
  { icon: MountainIcon, count: '230+', label: 'Destination' },
  { icon: AwardIcon, count: '230+', label: 'Award' },
];

const StatsBar: React.FC = () => {
  return (
    <section 
      className="py-16 lg:py-20 bg-cover bg-center relative text-white"
      style={{ backgroundImage: "url('https://picsum.photos/seed/statsbg/1920/400')" }}
      aria-label="Company Statistics"
    >
      <div className="absolute inset-0 bg-brand-blue-dark opacity-75"></div>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          {STATS_DATA.map((stat) => (
            <div key={stat.label} className="flex flex-col items-center">
              <stat.icon className="w-10 h-10 lg:w-12 lg:h-12 mb-3 text-brand-orange" />
              <p className="text-3xl lg:text-4xl font-bold font-poppins mb-1">{stat.count}</p>
              <p className="text-sm lg:text-base font-montserrat uppercase tracking-wider">{stat.label}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StatsBar;
