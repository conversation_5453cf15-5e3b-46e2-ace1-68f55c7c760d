
import React from 'react';

export const PlaneIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 18.75a.75.75 0 00.75-.75V6.36L17.5 9l2-4.5-2.5-.5-2.25 5.5-3.5-2-3.5 2L6 4l-2.5.5 2 4.5 4.75-2.64v12.64c0 .414.336.75.75.75h.5a.75.75 0 00.75-.75z" />
     <path strokeLinecap="round" strokeLinejoin="round" d="M3.375 19.5h17.25" />
  </svg>
);

export const BuildingIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h6m-6 4.5h6m-6 4.5h6M6.75 21v-3H8.25L9.75 6l1.5-3 1.5 3 1.5 12h1.5v3" />
  </svg>
);

export const CogIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93L15.5 6.35c.528.22 1.003.602 1.334 1.042l.658-.66c.39-.39.96-.39 1.35 0l.774.774c.39.39.39.96 0 1.35l-.66.658c.44.33.822.806 1.042 1.334l.444 1.027c.166.396.036.85-.294 1.11l-.894.149c-.424.07-.764.384-.93.78l-.224.932c-.22.528-.602 1.003-1.042 1.334l.66.658c.39.39.39.96 0 1.35l-.774.774c-.39.39-.96.39-1.35 0l-.658-.66c-.33.44-.806.822-1.334 1.042l-1.027.444c-.396.166-.85.036-1.11-.294l-.149-.894c-.07-.424-.384-.764-.78-.93L8.5 17.65c-.528-.22-1.003-.602-1.334-1.042l-.658.66c-.39.39-.96.39-1.35 0l-.774-.774c-.39-.39-.39-.96 0-1.35l.66-.658c-.44-.33-.822-.806-1.042-1.334l-.444-1.027c-.166-.396-.036-.85.294-1.11l.894-.149c.424-.07.764-.384.93-.78l.224-.932c.22-.528.602-1.003 1.042-1.334l-.66-.658c-.39-.39-.39-.96 0-1.35l.774-.774c.39-.39.96-.39 1.35 0l.658.66c.33-.44.806-.822 1.334-1.042l1.027-.444zM12 15.75a3.75 3.75 0 100-7.5 3.75 3.75 0 000 7.5z" />
  </svg>
);

export const BedIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 21h16.5M4.5 6.75A2.25 2.25 0 016.75 4.5h10.5a2.25 2.25 0 012.25 2.25v7.5H4.5v-7.5zM21 14.25H3v3.75a2.25 2.25 0 002.25 2.25h13.5a2.25 2.25 0 002.25-2.25v-3.75zM7.5 11.25a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
  </svg>
);

export const GlobeIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9 9 0 100-18 9 9 0 000 18zM12 3.75c-4.06 0-7.5 3.28-7.5 7.5 0 1.712.585 3.31 1.557 4.595M12 3.75c4.06 0 7.5 3.28 7.5 7.5 0 1.712-.585 3.31-1.557 4.595m-9.886 0A9.004 9.004 0 0012 20.25a9.004 9.004 0 005.443-1.36M3.75 12c0 .005 0 .01.001.015M20.25 12c0 .005 0 .01-.001.015M12 3.75v16.5m-5.405-9.75h10.81" />
  </svg>
);

export const ClockIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);
