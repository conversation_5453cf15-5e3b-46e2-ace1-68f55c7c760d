import React from 'react';
import ContactHero from '../components/ContactHero';
import ContactForm from '../components/ContactForm';
import ContactInfo from '../components/ContactInfo';
import MapSection from '../components/MapSection';
import Subscribe from '../components/Subscribe';

const ContactPage: React.FC = () => {
  return (
    <>
      <ContactHero />
      
      <section className="py-16 lg:py-24 bg-brand-gray-light">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-5 gap-12">
            <div className="lg:col-span-3">
              <ContactForm />
            </div>
            <div className="lg:col-span-2">
              <ContactInfo />
            </div>
          </div>
        </div>
      </section>

      <MapSection />
      <Subscribe />
    </>
  );
};

export default ContactPage;