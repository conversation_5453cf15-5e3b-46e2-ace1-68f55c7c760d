
import React from 'react';
import { TESTIMONIALS_DATA } from '../constants';
import type { Testimonial } from '../types';
import { StarIcon } from './icons/StarIcon';
import { QuoteIcon } from './icons/QuoteIcon';

const TestimonialCard: React.FC<{ testimonial: Testimonial }> = ({ testimonial }) => {
  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 !== 0;
    const starsArray = [];
    for (let i = 0; i < fullStars; i++) {
      starsArray.push(<StarIcon key={`full-${i}`} className="w-5 h-5 text-yellow-400" filled />);
    }
    if (halfStar) {
      // For simplicity, using full star for half star as well
      starsArray.push(<StarIcon key="half" className="w-5 h-5 text-yellow-400" filled />);
    }
    return starsArray;
  };

  return (
    <div className="eco-card p-8 rounded-2xl shadow-eco-lg flex flex-col items-center text-center group relative overflow-hidden">
      {/* Eco-friendly background pattern */}
      <div className="absolute top-0 right-0 w-16 h-16 bg-brand-mint/10 rounded-full blur-lg"></div>
      <div className="absolute bottom-0 left-0 w-12 h-12 bg-brand-green/10 rounded-full blur-md"></div>

      <div className="relative z-10">
        <img
          src={testimonial.avatar}
          alt={testimonial.name}
          className="w-24 h-24 rounded-full object-cover mb-6 border-4 border-brand-teal shadow-eco group-hover:scale-110 transition-transform duration-300"
        />
        <QuoteIcon className="w-8 h-8 text-brand-teal mb-4 group-hover:text-brand-mint transition-colors duration-300" />
        <p className="text-brand-gray-dark font-montserrat italic mb-6 text-base leading-relaxed">
          "{testimonial.review}"
        </p>
        <div className="flex items-center mb-3 justify-center">
          {renderStars(testimonial.rating)}
        </div>
        <h4 className="text-lg font-bold text-eco-primary font-poppins group-hover:text-brand-teal transition-colors duration-300">{testimonial.name}</h4>

        {/* Eco accent */}
        <div className="absolute top-3 right-3 w-2 h-2 bg-brand-mint rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>
    </div>
  );
};

const Testimonials: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 eco-section-light relative overflow-hidden">
      {/* Eco-friendly background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-32 h-32 bg-brand-teal rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-brand-mint rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-brand-green rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-eco-primary mb-4 font-poppins">
            What Our Eco-Travelers Say
          </h2>
          <p className="text-lg text-brand-gray-dark max-w-2xl mx-auto font-montserrat leading-relaxed">
            Hear from fellow eco-conscious travelers who experienced sustainable adventures and made a positive impact with Rebel Rover.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {TESTIMONIALS_DATA.map((testimonial) => (
            <TestimonialCard key={testimonial.id} testimonial={testimonial} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
